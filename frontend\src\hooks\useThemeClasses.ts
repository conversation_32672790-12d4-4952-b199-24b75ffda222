// Custom hook for theme-aware class generation
import { useTheme } from '../contexts/ThemeContext';
import { combineThemeClasses } from '../utils/theme';

export const useThemeClasses = () => {
  const { effectiveTheme, getThemeClasses, getComponentTheme, isDark } = useTheme();
  
  // Get theme classes
  const themeClasses = getThemeClasses();
  const componentTheme = getComponentTheme();
  
  // Utility function to combine multiple theme class paths
  const combine = (...classKeys: string[]) => {
    return combineThemeClasses(effectiveTheme as 'light' | 'dark', ...classKeys);
  };
  
  // Common component class generators
  const classes = {
    // Layout classes
    layout: {
      main: `${themeClasses.bg.primary} ${themeClasses.text.primary}`,
      sidebar: `${themeClasses.bg.sidebar} ${themeClasses.border.primary} border-r`,
      chat: `${themeClasses.bg.chat}`,
    },
    
    // Button classes
    button: {
      primary: componentTheme.button.primary,
      secondary: componentTheme.button.secondary,
      accent: componentTheme.button.accent,
      icon: `${componentTheme.button.icon} w-8 h-8 flex items-center justify-center`,
    },
    
    // Input classes
    input: {
      primary: componentTheme.input.primary,
      search: componentTheme.input.search,
    },
    
    // Card classes
    card: {
      primary: componentTheme.card.primary,
      conversation: `${componentTheme.card.conversation} p-3 rounded-lg`,
    },
    
    // Message classes
    message: {
      sent: componentTheme.message.sent,
      received: componentTheme.message.received,
      system: componentTheme.message.system,
    },
    
    // Text classes
    text: {
      primary: themeClasses.text.primary,
      secondary: themeClasses.text.secondary,
      tertiary: themeClasses.text.tertiary,
      accent: themeClasses.text.accent,
      placeholder: themeClasses.text.placeholder,
    },
    
    // Background classes
    bg: {
      primary: themeClasses.bg.primary,
      secondary: themeClasses.bg.secondary,
      tertiary: themeClasses.bg.tertiary,
      hover: themeClasses.bg.hover,
    },
    
    // Border classes
    border: {
      primary: themeClasses.border.primary,
      secondary: themeClasses.border.secondary,
      focus: themeClasses.border.focus,
    }
  };
  
  return {
    classes,
    themeClasses,
    componentTheme,
    combine,
    isDark,
    theme: effectiveTheme
  };
};
