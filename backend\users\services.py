# backend/users/services.py
import random
import string
from datetime import datetime, timedelta
from django.core.mail import send_mail
from django.conf import settings
from django.core.cache import cache
from django.utils import timezone
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from .models import OTP, OTPRateLimit, User
import logging

logger = logging.getLogger(__name__)


class OTPService:
    """Service for handling OTP generation, sending, and verification"""
    
    @staticmethod
    def generate_otp_code(length=6):
        """Generate a random OTP code"""
        return ''.join(random.choices(string.digits, k=length))
    
    @staticmethod
    def check_rate_limit(email):
        """Check if user has exceeded OTP request rate limit"""
        try:
            # For testing purposes, allow more requests
            rate_limit, created = OTPRateLimit.objects.get_or_create(
                email=email,
                defaults={
                    'request_count': 0,
                    'last_request': timezone.now()
                }
            )
            
            # Reset if needed (uses the model's method)
            rate_limit.reset_if_needed()
            
            # Check if blocked
            if rate_limit.is_blocked():
                return False, "Too many OTP requests. Please try again later."
            
            # Increase max requests for testing
            max_requests = getattr(settings, 'OTP_RATE_LIMIT_REQUESTS', 20)  # Increased from 5 to 20
            if rate_limit.request_count >= max_requests:
                # Block for 1 hour
                rate_limit.blocked_until = timezone.now() + timedelta(hours=1)
                rate_limit.save()
                return False, "Too many OTP requests. Please try again in 1 hour."
            
            # Increment request count
            rate_limit.increment_request()
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error checking rate limit for {email}: {str(e)}")
            return False, "Rate limit check failed. Please try again."
    
    @staticmethod
    def create_otp(email, purpose='login'):
        """Create a new OTP for the given email and purpose"""
        try:
            # Check rate limit first
            can_request, error_message = OTPService.check_rate_limit(email)
            if not can_request:
                return None, error_message
            
            # Invalidate any existing OTPs for this email and purpose
            OTP.objects.filter(
                email=email,
                purpose=purpose,
                is_used=False,
                expires_at__gt=timezone.now()
            ).update(is_used=True)
            
            # Generate new OTP
            code = OTPService.generate_otp_code()
            expiry_minutes = getattr(settings, 'OTP_EXPIRY_MINUTES', 10)
            expires_at = timezone.now() + timedelta(minutes=expiry_minutes)
            
            otp = OTP.objects.create(
                email=email,
                code=code,
                purpose=purpose,
                expires_at=expires_at
            )
            
            return otp, None
            
        except Exception as e:
            logger.error(f"Error creating OTP for {email}: {str(e)}")
            return None, "Failed to create OTP. Please try again."
    
    @staticmethod
    def send_otp_email(email, otp_code, purpose='login'):
        """Send OTP via email"""
        try:
            subject = f"Your {purpose.title()} OTP Code"
            
            # Create email context
            context = {
                'otp_code': otp_code,
                'purpose': purpose,
                'expiry_minutes': getattr(settings, 'OTP_EXPIRY_MINUTES', 10),
                'app_name': 'ChatApp'
            }
            
            # Create HTML and plain text versions
            html_message = f"""
            <html>
            <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="background-color: #f8f9fa; padding: 30px; border-radius: 10px; text-align: center;">
                    <h2 style="color: #333; margin-bottom: 20px;">Your {purpose.title()} OTP Code</h2>
                    <div style="background-color: #007bff; color: white; padding: 20px; border-radius: 5px; font-size: 24px; font-weight: bold; letter-spacing: 3px; margin: 20px 0;">
                        {otp_code}
                    </div>
                    <p style="color: #666; margin: 20px 0;">This code will expire in {context['expiry_minutes']} minutes.</p>
                    <p style="color: #666; font-size: 14px;">If you didn't request this code, please ignore this email.</p>
                    <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
                    <p style="color: #999; font-size: 12px;">This is an automated message from {context['app_name']}.</p>
                </div>
            </body>
            </html>
            """
            
            plain_message = f"""
Your {purpose.title()} OTP Code

Your verification code is: {otp_code}

This code will expire in {context['expiry_minutes']} minutes.

If you didn't request this code, please ignore this email.

This is an automated message from {context['app_name']}.
            """
            
            # Send email
            send_mail(
                subject=subject,
                message=plain_message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[email],
                html_message=html_message,
                fail_silently=False
            )
            
            logger.info(f"OTP email sent successfully to {email} for {purpose}")
            return True, None
            
        except Exception as e:
            logger.error(f"Error sending OTP email to {email}: {str(e)}")
            return False, "Failed to send OTP email. Please try again."
    
    @staticmethod
    def verify_otp(email, code, purpose='login'):
        """Verify OTP code"""
        try:
            otp = OTP.objects.filter(
                email=email,
                code=code,
                purpose=purpose,
                is_used=False,
                expires_at__gt=timezone.now()
            ).first()
            
            if not otp:
                return False, "Invalid or expired OTP code."
            
            # Mark OTP as used
            otp.is_used = True
            otp.save()
            
            logger.info(f"OTP verified successfully for {email} with purpose {purpose}")
            return True, None
            
        except Exception as e:
            logger.error(f"Error verifying OTP for {email}: {str(e)}")
            return False, "OTP verification failed. Please try again."
    
    @staticmethod
    def request_otp(email, purpose='login'):
        """Complete OTP request flow - create and send OTP"""
        try:
            # Create OTP
            otp, error = OTPService.create_otp(email, purpose)
            if not otp:
                return False, error
            
            # Send OTP email
            sent, send_error = OTPService.send_otp_email(email, otp.code, purpose)
            if not sent:
                # Mark OTP as used if email sending failed
                otp.is_used = True
                otp.save()
                return False, send_error
            
            return True, "OTP sent successfully to your email."
            
        except Exception as e:
            logger.error(f"Error in OTP request flow for {email}: {str(e)}")
            return False, "Failed to process OTP request. Please try again."


class UserService:
    """Service for user-related operations"""
    
    @staticmethod
    def user_exists(email):
        """Check if user exists by email"""
        return User.objects.filter(email=email).exists()
    
    @staticmethod
    def get_user_by_email(email):
        """Get user by email"""
        try:
            return User.objects.get(email=email)
        except User.DoesNotExist:
            return None
    
    @staticmethod
    def create_user(email, name, password=None):
        """Create a new user"""
        try:
            user = User.objects.create_user(
                email=email,
                name=name,
                password=password
            )
            logger.info(f"User created successfully: {email}")
            return user, None
        except Exception as e:
            logger.error(f"Error creating user {email}: {str(e)}")
            return None, "Failed to create user. Please try again."