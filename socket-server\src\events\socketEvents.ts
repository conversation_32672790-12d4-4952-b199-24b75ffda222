// socket-server/src/events/socketEvents.ts
import { Server, Socket } from 'socket.io';
import { MessageService } from '../services/messageService';
import { ConversationService } from '../services/conversationService';
import { MessageStatusService } from '../services/messageStatusService';
import { z } from 'zod';
import {
  MessageCreateSchema,
  JoinRoomSchema,
  TypingEventSchema,
  MessageStatusInputSchema
} from '../schemas';
import {
  SendMessageSchema,
  TypingIndicatorSchema,
  JoinConversationSchema,
  LeaveConversationSchema,
  KeyExchangeRequestSchema,
  KeyExchangeResponseSchema,
  validateSocketEvent,
  createSocketError,
  isEncryptedMessage,
  isPlaintextMessage,
  type SendMessage,
  type TypingIndicator,
  type KeyExchangeRequest,
  type KeyExchangeResponse,
} from '../schemas/encryptionSchemas';
import {
  MediaUploadStartedSchema,
  MediaUploadProgressSchema,
  MediaUploadCompletedSchema,
  MediaUploadFailedSchema,
  MediaDownloadStartedSchema,
  validateMediaEvent
} from '../schemas/mediaSchemas';
import { EncryptionService } from '../services/encryptionService';

interface AuthenticatedSocket extends Socket {
  userId: string;
  user: any;
}

export class SocketEventHandler {
  private io: Server;
  private messageService: MessageService;
  private conversationService: ConversationService;
  private messageStatusService: MessageStatusService;
  private encryptionService: EncryptionService;

  constructor(
    io: Server,
    messageService: MessageService,
    conversationService: ConversationService,
    messageStatusService: MessageStatusService,
    encryptionService: EncryptionService
  ) {
    this.io = io;
    this.messageService = messageService;
    this.conversationService = conversationService;
    this.messageStatusService = messageStatusService;
    this.encryptionService = encryptionService;
  }

  handleConnection(socket: AuthenticatedSocket) {
    console.log(`User ${socket.userId} connected to messaging`);

    // Join user to their conversations
    socket.on('join_conversations', () => this.handleJoinConversations(socket));

    // Handle new message creation (supports both encrypted and plaintext)
    socket.on('send_message', (data) => this.handleSendMessage(socket, data));

    // Handle typing indicators
    socket.on('typing_start', (data) => this.handleTypingStart(socket, data));
    socket.on('typing_stop', (data) => this.handleTypingStop(socket, data));

    // Phase 3: Handle encryption-specific events
    socket.on('key_exchange_request', (data) => this.handleKeyExchangeRequest(socket, data));
    socket.on('encryption_status_check', (data) => this.handleEncryptionStatusCheck(socket, data));

    // Phase 4: Handle group-specific events
    socket.on('group_member_added', (data) => this.handleGroupMemberAdded(socket, data));
    socket.on('group_member_removed', (data) => this.handleGroupMemberRemoved(socket, data));
    socket.on('group_info_updated', (data) => this.handleGroupInfoUpdated(socket, data));
    socket.on('group_keys_rotated', (data) => this.handleGroupKeysRotated(socket, data));

    // Handle joining specific conversation room
    socket.on('join_conversation', (data) => this.handleJoinConversation(socket, data));

    // Handle user status
    socket.on('user_online', () => this.handleUserOnline(socket));

    // Handle message status updates
    socket.on('message_delivered', (data) => this.handleMessageDelivered(socket, data));
    socket.on('message_read', (data) => this.handleMessageRead(socket, data));
    socket.on('message_failed', (data) => this.handleMessageFailed(socket, data));

    // Phase 5: Handle media-related events
    socket.on('media_upload_started', (data) => this.handleMediaUploadStarted(socket, data));
    socket.on('media_upload_progress', (data) => this.handleMediaUploadProgress(socket, data));
    socket.on('media_upload_completed', (data) => this.handleMediaUploadCompleted(socket, data));
    socket.on('media_upload_failed', (data) => this.handleMediaUploadFailed(socket, data));
    socket.on('media_download_started', (data) => this.handleMediaDownloadStarted(socket, data));

    // Handle disconnection
    socket.on('disconnect', () => this.handleDisconnect(socket));
  }

  private async handleJoinConversations(socket: AuthenticatedSocket) {
    try {
      const conversations = await this.conversationService.getUserConversations(socket.userId);

      // Join all conversation rooms
      for (const conversation of conversations) {
        socket.join(`conversation_${conversation.id}`);
      }

      socket.emit('conversations_joined', {
        success: true,
        count: conversations.length,
        conversations: conversations
      });
    } catch (error) {
      console.error('Error joining conversations:', error);
      socket.emit('error', { message: 'Failed to join conversations' });
    }
  }

  private async handleSendMessage(socket: AuthenticatedSocket, data: any) {
    try {
      // Validate message data (supports both encrypted and plaintext)
      const validatedData = validateSocketEvent(SendMessageSchema, data);

      // Check if this is an encrypted message
      const isEncrypted = isEncryptedMessage(validatedData);

      // Log message type (without content for security)
      console.log(`User ${socket.userId} sending ${isEncrypted ? 'encrypted' : 'plaintext'} message to conversation ${validatedData.conversationId}`);

      // Create message in database
      const message = await this.messageService.createMessage(validatedData, socket.userId);

      // Create DELIVERED status for the sender
      await this.messageStatusService.markMessageAsDelivered(message.id, socket.userId);

      // Prepare message payload for broadcasting
      const messagePayload = {
        id: message.id,
        conversationId: message.conversationId,
        sender: message.sender,
        messageType: message.messageType,
        createdAt: message.createdAt,
        updatedAt: message.updatedAt,
        status: 'DELIVERED',
        // Include appropriate content based on message type
        ...(isEncrypted ? {
          // For encrypted messages, include encrypted fields
          encryptedContent: message.encryptedContent,
          iv: message.iv,
          senderRatchetKey: message.senderRatchetKey,
          messageNumber: message.messageNumber,
          previousChainLength: message.previousChainLength,
          isEncrypted: true,
        } : {
          // For plaintext messages, include content
          content: message.content,
          isEncrypted: false,
        })
      };

      // Emit to all participants in the conversation
      this.io.to(`conversation_${message.conversationId}`).emit('new_message', messagePayload);

      // Acknowledge to sender
      socket.emit('message_sent', {
        tempId: data.tempId, // For optimistic UI updates
        messageId: message.id,
        status: 'DELIVERED',
        isEncrypted
      });

      // Log successful message delivery (without sensitive content)
      console.log(`Message ${message.id} delivered successfully (encrypted: ${isEncrypted})`);

    } catch (error) {
      console.error('Error sending message:', error);

      // Mark message as failed if it exists
      if (data.tempId) {
        socket.emit('message_failed', {
          tempId: data.tempId,
          error: error instanceof Error ? error.message : 'Failed to send message'
        });
      }

      if (error instanceof z.ZodError) {
        socket.emit('error', createSocketError(
          'Invalid message data',
          'VALIDATION_ERROR',
          { issues: error.issues }
        ));
      } else {
        socket.emit('error', createSocketError('Failed to send message', 'MESSAGE_SEND_ERROR'));
      }
    }
  }

  private async handleTypingStart(socket: AuthenticatedSocket, data: any) {
    try {
      // Check if this is a draft conversation (doesn't need UUID validation)
      const isDraftConversation = data?.conversationId?.startsWith('draft-');
      
      if (isDraftConversation) {
        // For draft conversations, just validate basic structure without UUID validation
        if (!data?.conversationId || typeof data.conversationId !== 'string') {
          socket.emit('error', createSocketError(
            'Invalid conversation ID',
            'VALIDATION_ERROR'
          ));
          return;
        }
        
        // Emit typing event for draft conversation (no server-side verification needed)
        socket.to(`conversation_${data.conversationId}`).emit('user_typing', {
          userId: socket.userId,
          conversationId: data.conversationId,
          isTyping: true
        });
        return;
      }

      // For real conversations, use full validation
      const typingData = TypingEventSchema.parse(data);

      // Verify access to conversation
      const hasAccess = await this.messageService.verifyConversationAccess(
        socket.userId,
        typingData.conversationId
      );

      if (hasAccess) {
        socket.to(`conversation_${typingData.conversationId}`).emit('user_typing', {
          userId: socket.userId,
          conversationId: typingData.conversationId,
          isTyping: true
        });
      }
    } catch (error) {
      console.error('Error handling typing start:', error);
      if (error instanceof z.ZodError) {
        socket.emit('error', createSocketError(
          'Invalid typing event data',
          'VALIDATION_ERROR',
          { issues: error.issues }
        ));
      } else {
        socket.emit('error', createSocketError('Failed to process typing start', 'TYPING_ERROR'));
      }
    }
  }

  private async handleTypingStop(socket: AuthenticatedSocket, data: any) {
    try {
      // Check if this is a draft conversation (doesn't need UUID validation)
      const isDraftConversation = data?.conversationId?.startsWith('draft-');
      
      if (isDraftConversation) {
        // For draft conversations, just validate basic structure without UUID validation
        if (!data?.conversationId || typeof data.conversationId !== 'string') {
          socket.emit('error', createSocketError(
            'Invalid conversation ID',
            'VALIDATION_ERROR'
          ));
          return;
        }
        
        // Emit typing event for draft conversation (no server-side verification needed)
        socket.to(`conversation_${data.conversationId}`).emit('user_typing', {
          userId: socket.userId,
          conversationId: data.conversationId,
          isTyping: false
        });
        return;
      }

      // For real conversations, use full validation
      const typingData = TypingEventSchema.parse(data);

      const hasAccess = await this.messageService.verifyConversationAccess(
        socket.userId,
        typingData.conversationId
      );

      if (hasAccess) {
        socket.to(`conversation_${typingData.conversationId}`).emit('user_typing', {
          userId: socket.userId,
          conversationId: typingData.conversationId,
          isTyping: false
        });
      }
    } catch (error) {
      console.error('Error handling typing stop:', error);
      if (error instanceof z.ZodError) {
        socket.emit('error', createSocketError(
          'Invalid typing event data',
          'VALIDATION_ERROR',
          { issues: error.issues }
        ));
      } else {
        socket.emit('error', createSocketError('Failed to process typing stop', 'TYPING_ERROR'));
      }
    }
  }

  private async handleJoinConversation(socket: AuthenticatedSocket, data: any) {
    try {
      const { conversationId } = JoinRoomSchema.parse(data);

      const hasAccess = await this.conversationService.joinConversation(socket.userId, conversationId);
      if (hasAccess) {
        socket.join(`conversation_${conversationId}`);
        socket.emit('joined_conversation', { conversationId });
      } else {
        socket.emit('error', { message: 'Access denied to conversation' });
      }
    } catch (error) {
      console.error('Error joining conversation:', error);
      socket.emit('error', { message: 'Failed to join conversation' });
    }
  }

  private async handleUserOnline(socket: AuthenticatedSocket) {
    try {
      await this.messageService.updateUserStatus(socket.userId, true);

      socket.broadcast.emit('user_status_change', {
        userId: socket.userId,
        status: 'online'
      });
    } catch (error) {
      console.error('Error updating user online status:', error);
    }
  }

  private async handleDisconnect(socket: AuthenticatedSocket) {
    console.log(`User ${socket.userId} disconnected from messaging`);

    try {
      await this.messageService.updateUserStatus(socket.userId, false);

      socket.broadcast.emit('user_status_change', {
        userId: socket.userId,
        status: 'offline'
      });
    } catch (error) {
      console.error('Error updating user offline status:', error);
    }
  }

  private async handleMessageDelivered(socket: AuthenticatedSocket, data: any) {
    try {
      const validatedData = MessageStatusInputSchema.parse(data);

      const messageStatus = await this.messageStatusService.markMessageAsDelivered(
        validatedData.messageId,
        socket.userId
      );

      // Emit to the message sender
      const message = await this.messageService.getMessageById(validatedData.messageId);
      if (message) {
        this.io.to(`conversation_${message.conversationId}`).emit('message_status_updated', {
          messageId: validatedData.messageId,
          userId: socket.userId,
          status: 'DELIVERED',
          tempId: validatedData.tempId,
          updatedAt: messageStatus.updatedAt
        });
      }
    } catch (error) {
      console.error('Error handling message delivered:', error);
      socket.emit('error', {
        message: 'Failed to update message status',
        details: error instanceof z.ZodError ? error.issues : undefined
      });
    }
  }

  private async handleMessageRead(socket: AuthenticatedSocket, data: any) {
    try {
      const validatedData = MessageStatusInputSchema.parse(data);

      // Update all message statuses for this message to 'READ'
      const updatedStatuses = await this.messageStatusService.markAllMessageStatusesAsRead(
        validatedData.messageId,
        socket.userId
      );

      // Get the message to find the conversation
      const message = await this.messageService.getMessageById(validatedData.messageId);
      if (message && updatedStatuses.length > 0) {
        // Emit status updates for all affected users
        updatedStatuses.forEach(status => {
          this.io.to(`conversation_${message.conversationId}`).emit('message_status_updated', {
            messageId: validatedData.messageId,
            userId: status.userId,
            status: 'READ',
            tempId: validatedData.tempId,
            updatedAt: status.updatedAt
          });
        });
      }
    } catch (error) {
      console.error('Error handling message read:', error);
      socket.emit('error', {
        message: 'Failed to update message status',
        details: error instanceof z.ZodError ? error.issues : undefined
      });
    }
  }

  private async handleMessageFailed(socket: AuthenticatedSocket, data: any) {
    try {
      const validatedData = MessageStatusInputSchema.parse(data);

      const messageStatus = await this.messageStatusService.markMessageAsFailed(
        validatedData.messageId,
        socket.userId
      );

      // Emit back to the sender
      socket.emit('message_status_updated', {
        messageId: validatedData.messageId,
        userId: socket.userId,
        status: 'FAILED',
        tempId: validatedData.tempId,
        updatedAt: messageStatus.updatedAt
      });
    } catch (error) {
      console.error('Error handling message failed:', error);
      socket.emit('error', {
        message: 'Failed to update message status',
        details: error instanceof z.ZodError ? error.issues : undefined
      });
    }
  }

  // Phase 3: Encryption-specific event handlers

  /**
   * Handle key exchange request between users
   */
  private async handleKeyExchangeRequest(socket: AuthenticatedSocket, data: any) {
    try {
      const validatedData = validateSocketEvent(KeyExchangeRequestSchema, data);

      console.log(`Key exchange request from ${socket.userId} for user ${validatedData.targetUserId}`);

      // Get target user's key bundle
      const keyBundle = await this.encryptionService.getUserKeyBundle(
        validatedData.targetUserId,
        socket.userId
      );

      if (!keyBundle) {
        socket.emit('key_exchange_response', createSocketError(
          'Target user does not have encryption enabled',
          'NO_KEY_BUNDLE'
        ));
        return;
      }

      // Send key bundle back to requester
      const response: KeyExchangeResponse = {
        success: true,
        keyBundle,
      };

      socket.emit('key_exchange_response', response);

      console.log(`Key bundle sent to ${socket.userId} for user ${validatedData.targetUserId}`);

    } catch (error) {
      console.error('Error handling key exchange request:', error);
      socket.emit('key_exchange_response', createSocketError(
        'Failed to process key exchange request',
        'KEY_EXCHANGE_ERROR'
      ));
    }
  }

  /**
   * Check encryption status for a conversation
   */
  private async handleEncryptionStatusCheck(socket: AuthenticatedSocket, data: any) {
    try {
      const { conversationId } = data;

      if (!conversationId) {
        socket.emit('encryption_status_response', createSocketError(
          'Conversation ID required',
          'MISSING_CONVERSATION_ID'
        ));
        return;
      }

      // Check if conversation supports encryption
      const isEncrypted = await this.encryptionService.isConversationEncrypted(conversationId);
      const participants = await this.encryptionService.getConversationParticipants(conversationId);

      socket.emit('encryption_status_response', {
        conversationId,
        isEncrypted,
        participants: participants.map(p => ({
          id: p.id,
          name: p.name,
          hasEncryption: p.hasEncryption,
        })),
      });

    } catch (error) {
      console.error('Error checking encryption status:', error);
      socket.emit('encryption_status_response', createSocketError(
        'Failed to check encryption status',
        'ENCRYPTION_STATUS_ERROR'
      ));
    }
  }

  // PHASE 4: Group Event Handlers

  /**
   * Handle group member added event
   */
  private async handleGroupMemberAdded(socket: AuthenticatedSocket, data: any) {
    try {
      const { conversationId, newMember, addedBy } = data;

      if (!conversationId || !newMember || !addedBy) {
        socket.emit('error', { message: 'Invalid group member added data' });
        return;
      }

      // Broadcast to all group members
      this.io.to(`conversation_${conversationId}`).emit('group_member_added', {
        conversationId,
        newMember,
        addedBy,
        timestamp: new Date().toISOString()
      });

      console.log(`Member ${newMember.id} added to group ${conversationId} by ${addedBy.id}`);

    } catch (error) {
      console.error('Error handling group member added:', error);
      socket.emit('error', { message: 'Failed to handle group member added event' });
    }
  }

  /**
   * Handle group member removed event
   */
  private async handleGroupMemberRemoved(socket: AuthenticatedSocket, data: any) {
    try {
      const { conversationId, removedMember, removedBy, reason } = data;

      if (!conversationId || !removedMember || !removedBy) {
        socket.emit('error', { message: 'Invalid group member removed data' });
        return;
      }

      // Broadcast to all group members
      this.io.to(`conversation_${conversationId}`).emit('group_member_removed', {
        conversationId,
        removedMember,
        removedBy,
        reason: reason || 'removed',
        timestamp: new Date().toISOString()
      });

      console.log(`Member ${removedMember.id} ${reason || 'removed'} from group ${conversationId} by ${removedBy.id}`);

    } catch (error) {
      console.error('Error handling group member removed:', error);
      socket.emit('error', { message: 'Failed to handle group member removed event' });
    }
  }

  /**
   * Handle group info updated event
   */
  private async handleGroupInfoUpdated(socket: AuthenticatedSocket, data: any) {
    try {
      const { conversationId, updatedInfo, updatedBy } = data;

      if (!conversationId || !updatedInfo || !updatedBy) {
        socket.emit('error', { message: 'Invalid group info updated data' });
        return;
      }

      // Broadcast to all group members
      this.io.to(`conversation_${conversationId}`).emit('group_info_updated', {
        conversationId,
        updatedInfo,
        updatedBy,
        timestamp: new Date().toISOString()
      });

      console.log(`Group ${conversationId} info updated by ${updatedBy.id}`);

    } catch (error) {
      console.error('Error handling group info updated:', error);
      socket.emit('error', { message: 'Failed to handle group info updated event' });
    }
  }

  /**
   * Handle group keys rotated event
   */
  private async handleGroupKeysRotated(socket: AuthenticatedSocket, data: any) {
    try {
      const { conversationId, newEpoch, reason, rotatedBy } = data;

      if (!conversationId || newEpoch === undefined || !rotatedBy) {
        socket.emit('error', { message: 'Invalid group keys rotated data' });
        return;
      }

      // Broadcast to all group members
      this.io.to(`conversation_${conversationId}`).emit('group_keys_rotated', {
        conversationId,
        newEpoch,
        reason: reason || 'manual',
        rotatedBy,
        timestamp: new Date().toISOString()
      });

      console.log(`Group ${conversationId} keys rotated to epoch ${newEpoch} by ${rotatedBy.id} (reason: ${reason})`);

    } catch (error) {
      console.error('Error handling group keys rotated:', error);
      socket.emit('error', { message: 'Failed to handle group keys rotated event' });
    }
  }

  // PHASE 5: Media Event Handlers

  /**
   * Handle media upload started notification
   */
  private async handleMediaUploadStarted(socket: AuthenticatedSocket, data: any) {
    try {
      // Validate input data
      const validatedData = validateMediaEvent(MediaUploadStartedSchema, data);
      const { conversationId, messageId, fileName, fileSize, fileType, tempId } = validatedData;

      // Verify access to conversation
      const hasAccess = await this.messageService.verifyConversationAccess(
        socket.userId,
        conversationId
      );

      if (!hasAccess) {
        socket.emit('error', { message: 'Access denied to conversation' });
        return;
      }

      // Notify other participants that a media upload has started
      socket.to(`conversation_${conversationId}`).emit('media_upload_started', {
        conversationId,
        messageId,
        uploaderId: socket.userId,
        fileName,
        fileSize,
        fileType,
        tempId,
        timestamp: new Date().toISOString()
      });

      console.log(`Media upload started by ${socket.userId} in conversation ${conversationId}: ${fileName}`);

    } catch (error) {
      console.error('Error handling media upload started:', error);
      socket.emit('error', { message: 'Failed to handle media upload started event' });
    }
  }

  /**
   * Handle media upload progress updates
   */
  private async handleMediaUploadProgress(socket: AuthenticatedSocket, data: any) {
    try {
      // Validate input data
      const validatedData = validateMediaEvent(MediaUploadProgressSchema, data);
      const { conversationId, messageId, progress, tempId } = validatedData;

      // Verify access to conversation
      const hasAccess = await this.messageService.verifyConversationAccess(
        socket.userId,
        conversationId
      );

      if (!hasAccess) {
        socket.emit('error', { message: 'Access denied to conversation' });
        return;
      }

      // Notify other participants of upload progress
      socket.to(`conversation_${conversationId}`).emit('media_upload_progress', {
        conversationId,
        messageId,
        uploaderId: socket.userId,
        progress,
        tempId,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error handling media upload progress:', error);
      socket.emit('error', { message: 'Failed to handle media upload progress event' });
    }
  }

  /**
   * Handle media upload completed from HTTP endpoint (Django backend)
   */
  public async handleMediaUploadCompletedFromHttp(data: any, room: string) {
    try {
      console.log('Processing media upload completed from HTTP:', data);

      const { conversationId, messageId, mediaFileId, tempId } = data;

      // Since Django handles media files and the Socket.IO server doesn't have access to them,
      // we'll create a simplified message payload and let the frontend handle the media display
      const mediaMessagePayload = {
        id: messageId,
        conversationId: conversationId,
        sender: {
          id: 'system', // We don't have sender info from Django's HTTP request
          name: 'System',
          email: '<EMAIL>',
          profile_picture: null
        },
        content: `📎 Media file uploaded`, // Simple content for media messages
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        messageType: 'MEDIA',
        mediaFileId: mediaFileId, // Include the media file ID
        tempId: tempId
      };

      console.log('Broadcasting media message to room:', room);

      // Broadcast the completed media message to all participants
      this.io.to(room).emit('media_message_received', mediaMessagePayload);

      // Also emit as new_message for frontend SocketContext compatibility
      this.io.to(room).emit('new_message', mediaMessagePayload);

      console.log(`Media upload completed via HTTP: message ${messageId}, media ${mediaFileId}`);

    } catch (error) {
      console.error('Error handling media upload completed from HTTP:', error);
    }
  }

  /**
   * Handle media upload completed notification
   */
  private async handleMediaUploadCompleted(socket: AuthenticatedSocket, data: any) {
    try {
      // Validate input data
      const validatedData = validateMediaEvent(MediaUploadCompletedSchema, data);
      const { conversationId, messageId, mediaFileId, tempId } = validatedData;

      // Verify access to conversation
      const hasAccess = await this.messageService.verifyConversationAccess(
        socket.userId,
        conversationId
      );

      if (!hasAccess) {
        socket.emit('error', { message: 'Access denied to conversation' });
        return;
      }

      // Get the updated message with media information
      const message = await this.messageService.getMessageById(messageId);
      if (!message) {
        socket.emit('error', { message: 'Message not found' });
        return;
      }

      // Prepare media message payload (simplified since we don't have sender info)
      const mediaMessagePayload = {
        id: message.id,
        conversationId: message.conversationId,
        sender: {
          id: socket.userId,
          name: 'User',
          email: '<EMAIL>',
          profile_picture: null
        },
        messageType: 'FILE', // Update message type to FILE
        hasMedia: true,
        mediaCount: 1,
        mediaFileId,
        createdAt: message.createdAt,
        updatedAt: message.updatedAt,
        tempId,
        // Include encrypted content if available
        ...(message.encryptedContent ? {
          encryptedContent: message.encryptedContent,
          iv: message.iv,
          senderRatchetKey: message.senderRatchetKey,
          messageNumber: message.messageNumber,
          previousChainLength: message.previousChainLength,
          isEncrypted: true,
        } : {
          content: message.content,
          isEncrypted: false,
        })
      };

      // Broadcast the completed media message to all participants
      this.io.to(`conversation_${conversationId}`).emit('media_message_received', mediaMessagePayload);
      
      // Also emit as new_message for frontend SocketContext compatibility
      this.io.to(`conversation_${conversationId}`).emit('new_message', mediaMessagePayload);

      // Acknowledge to uploader
      socket.emit('media_upload_completed', {
        messageId,
        mediaFileId,
        tempId,
        status: 'completed',
        timestamp: new Date().toISOString()
      });

      console.log(`Media upload completed by ${socket.userId}: message ${messageId}, media ${mediaFileId}`);

    } catch (error) {
      console.error('Error handling media upload completed:', error);
      socket.emit('error', { message: 'Failed to handle media upload completed event' });
    }
  }

  /**
   * Handle media upload failed notification
   */
  private async handleMediaUploadFailed(socket: AuthenticatedSocket, data: any) {
    try {
      // Validate input data
      const validatedData = validateMediaEvent(MediaUploadFailedSchema, data);
      const { conversationId, messageId, error, tempId } = validatedData;

      // Verify access to conversation
      const hasAccess = await this.messageService.verifyConversationAccess(
        socket.userId,
        conversationId
      );

      if (!hasAccess) {
        socket.emit('error', { message: 'Access denied to conversation' });
        return;
      }

      // Notify other participants that upload failed
      socket.to(`conversation_${conversationId}`).emit('media_upload_failed', {
        conversationId,
        messageId,
        uploaderId: socket.userId,
        error,
        tempId,
        timestamp: new Date().toISOString()
      });

      // Acknowledge to uploader
      socket.emit('media_upload_failed', {
        messageId,
        tempId,
        error,
        timestamp: new Date().toISOString()
      });

      console.log(`Media upload failed by ${socket.userId}: message ${messageId}, error: ${error}`);

    } catch (error) {
      console.error('Error handling media upload failed:', error);
      socket.emit('error', { message: 'Failed to handle media upload failed event' });
    }
  }

  /**
   * Handle media download started notification
   */
  private async handleMediaDownloadStarted(socket: AuthenticatedSocket, data: any) {
    try {
      // Validate input data
      const validatedData = validateMediaEvent(MediaDownloadStartedSchema, data);
      const { conversationId, mediaFileId, fileName } = validatedData;

      // Verify access to conversation
      const hasAccess = await this.messageService.verifyConversationAccess(
        socket.userId,
        conversationId
      );

      if (!hasAccess) {
        socket.emit('error', { message: 'Access denied to conversation' });
        return;
      }

      // Optional: Notify other participants that someone is downloading media
      // This could be used for analytics or UI indicators
      socket.to(`conversation_${conversationId}`).emit('media_download_started', {
        conversationId,
        mediaFileId,
        downloaderId: socket.userId,
        fileName,
        timestamp: new Date().toISOString()
      });

      console.log(`Media download started by ${socket.userId}: ${mediaFileId} (${fileName})`);

    } catch (error) {
      console.error('Error handling media download started:', error);
      socket.emit('error', { message: 'Failed to handle media download started event' });
    }
  }
}
