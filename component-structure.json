[{"name": "App.tsx", "type": "file", "path": "App.tsx", "calls": ["components/calling/index.ts", "components/ui/ErrorBoundary.tsx", "contexts/AuthContext.tsx", "contexts/EncryptionContext.tsx", "contexts/SocketContext.tsx", "contexts/ThemeContext.tsx", "hooks/useSocketCacheSync.ts", "pages/Dashboard.tsx", "pages/Login.tsx", "pages/NewChatUI.tsx", "pages/Register.tsx", "store/index.ts"], "children": []}, {"name": "components", "type": "folder", "path": "components", "calls": [], "children": [{"name": "Cha<PERSON>", "type": "folder", "path": "components/Chat", "calls": [], "children": [{"name": "ChatHeader.tsx", "type": "file", "path": "components/Chat/ChatHeader.tsx", "calls": ["components/ui/ProfilePhotoAvatar.tsx", "contexts/SocketContext.tsx", "hooks/useThemeClasses.ts", "services/callingApi.ts", "store/index.ts", "store/slices/callingSlice.ts", "store/slices/conversationSlice.ts"], "children": []}, {"name": "SimpleMediaUpload.tsx", "type": "file", "path": "components/Chat/SimpleMediaUpload.tsx", "calls": ["contexts/SocketContext.tsx", "hooks/useMediaEncryption.ts", "services/mediaApi.ts"], "children": []}]}, {"name": "Icons", "type": "folder", "path": "components/Icons", "calls": [], "children": [{"name": "Google.tsx", "type": "file", "path": "components/Icons/Google.tsx", "calls": [], "children": []}]}, {"name": "calling", "type": "folder", "path": "components/calling", "calls": [], "children": [{"name": "ActiveCallInterface.tsx", "type": "file", "path": "components/calling/ActiveCallInterface.tsx", "calls": ["components/calling/CallControls.tsx", "components/calling/CallStatusIndicator.tsx", "components/calling/VideoContainer.tsx", "store/slices/callingSlice.ts"], "children": []}, {"name": "CallControls.tsx", "type": "file", "path": "components/calling/CallControls.tsx", "calls": ["contexts/SocketContext.tsx", "services/callingApi.ts", "store/index.ts", "store/slices/callingSlice.ts"], "children": []}, {"name": "CallManager.tsx", "type": "file", "path": "components/calling/CallManager.tsx", "calls": ["components/calling/ActiveCallInterface.tsx", "components/calling/IncomingCallModal.tsx", "contexts/SocketContext.tsx", "services/webrtc/index.ts", "store/index.ts", "store/slices/callingSlice.ts"], "children": []}, {"name": "CallStatusIndicator.tsx", "type": "file", "path": "components/calling/CallStatusIndicator.tsx", "calls": ["store/slices/callingSlice.ts"], "children": []}, {"name": "IncomingCallModal.tsx", "type": "file", "path": "components/calling/IncomingCallModal.tsx", "calls": ["services/callingApi.ts", "store/slices/callingSlice.ts"], "children": []}, {"name": "VideoContainer.tsx", "type": "file", "path": "components/calling/VideoContainer.tsx", "calls": ["store/slices/callingSlice.ts"], "children": []}, {"name": "index.ts", "type": "file", "path": "components/calling/index.ts", "calls": ["components/calling/ActiveCallInterface.tsx", "components/calling/CallControls.tsx", "components/calling/CallManager.tsx", "components/calling/CallStatusIndicator.tsx", "components/calling/IncomingCallModal.tsx", "components/calling/VideoContainer.tsx"], "children": []}]}, {"name": "ui", "type": "folder", "path": "components/ui", "calls": [], "children": [{"name": "ApiErrorDisplay.tsx", "type": "file", "path": "components/ui/ApiErrorDisplay.tsx", "calls": ["components/ui/Button.tsx", "components/ui/Icon.tsx", "hooks/useApiError.ts"], "children": []}, {"name": "Button.tsx", "type": "file", "path": "components/ui/Button.tsx", "calls": ["components/ui/Icon.tsx", "constants/designSystem.ts"], "children": []}, {"name": "ConversationItem.tsx", "type": "file", "path": "components/ui/ConversationItem.tsx", "calls": ["components/ui/Icon.tsx", "components/ui/ProfilePhotoAvatar.tsx"], "children": []}, {"name": "Divider.tsx", "type": "file", "path": "components/ui/Divider.tsx", "calls": [], "children": []}, {"name": "ErrorBoundary.tsx", "type": "file", "path": "components/ui/ErrorBoundary.tsx", "calls": ["components/ui/Button.tsx", "components/ui/Icon.tsx"], "children": []}, {"name": "Form.tsx", "type": "file", "path": "components/ui/Form.tsx", "calls": ["components/ui/Button.tsx", "components/ui/Icon.tsx"], "children": []}, {"name": "Icon.tsx", "type": "file", "path": "components/ui/Icon.tsx", "calls": ["components/Icons/Google.tsx"], "children": []}, {"name": "Input.tsx", "type": "file", "path": "components/ui/Input.tsx", "calls": ["components/ui/Icon.tsx", "constants/designSystem.ts"], "children": []}, {"name": "LoadingSpinner.tsx", "type": "file", "path": "components/ui/LoadingSpinner.tsx", "calls": ["components/ui/Icon.tsx"], "children": []}, {"name": "MessageInput.tsx", "type": "file", "path": "components/ui/MessageInput.tsx", "calls": ["components/Chat/SimpleMediaUpload.tsx", "components/ui/Button.tsx", "components/ui/Icon.tsx", "components/ui/TextField.tsx", "contexts/SocketContext.tsx", "store/index.ts"], "children": []}, {"name": "ProfilePhotoAvatar.tsx", "type": "file", "path": "components/ui/ProfilePhotoAvatar.tsx", "calls": ["components/ui/Icon.tsx", "constants/designSystem.ts"], "children": []}, {"name": "SearchBar.tsx", "type": "file", "path": "components/ui/SearchBar.tsx", "calls": ["components/ui/Icon.tsx"], "children": []}, {"name": "Select.tsx", "type": "file", "path": "components/ui/Select.tsx", "calls": ["components/ui/Icon.tsx"], "children": []}, {"name": "Sidebar.tsx", "type": "file", "path": "components/ui/Sidebar.tsx", "calls": ["components/ui/Button.tsx", "components/ui/ConversationItem.tsx", "components/ui/Icon.tsx", "components/ui/SearchBar.tsx", "components/ui/ThemeToggle.tsx", "contexts/AuthContext.tsx", "hooks/useThemeClasses.ts", "services/conversationApi.ts", "services/index.ts", "services/userApi.ts"], "children": []}, {"name": "TextField.tsx", "type": "file", "path": "components/ui/TextField.tsx", "calls": ["components/ui/Icon.tsx"], "children": []}, {"name": "ThemeToggle.tsx", "type": "file", "path": "components/ui/ThemeToggle.tsx", "calls": ["components/ui/Icon.tsx", "constants/designSystem.ts", "contexts/ThemeContext.tsx", "hooks/useThemeClasses.ts"], "children": []}, {"name": "index.ts", "type": "file", "path": "components/ui/index.ts", "calls": ["components/ui/ApiErrorDisplay.tsx", "components/ui/Button.tsx", "components/ui/ConversationItem.tsx", "components/ui/Divider.tsx", "components/ui/ErrorBoundary.tsx", "components/ui/Form.tsx", "components/ui/Icon.tsx", "components/ui/Input.tsx", "components/ui/LoadingSpinner.tsx", "components/ui/MessageInput.tsx", "components/ui/ProfilePhotoAvatar.tsx", "components/ui/SearchBar.tsx", "components/ui/Select.tsx", "components/ui/Sidebar.tsx", "components/ui/TextField.tsx", "components/ui/ThemeToggle.tsx", "components/ui/messages/index.ts", "constants/designSystem.ts"], "children": []}, {"name": "messages", "type": "folder", "path": "components/ui/messages", "calls": [], "children": [{"name": "AudioMessage.tsx", "type": "file", "path": "components/ui/messages/AudioMessage.tsx", "calls": ["components/ui/Icon.tsx"], "children": []}, {"name": "CallingMessage.tsx", "type": "file", "path": "components/ui/messages/CallingMessage.tsx", "calls": ["components/ui/Icon.tsx"], "children": []}, {"name": "FileMessage.tsx", "type": "file", "path": "components/ui/messages/FileMessage.tsx", "calls": ["components/ui/Icon.tsx"], "children": []}, {"name": "GroupUpdateMessage.tsx", "type": "file", "path": "components/ui/messages/GroupUpdateMessage.tsx", "calls": ["components/ui/Icon.tsx", "components/ui/ProfilePhotoAvatar.tsx"], "children": []}, {"name": "ImageMessage.tsx", "type": "file", "path": "components/ui/messages/ImageMessage.tsx", "calls": ["components/ui/Icon.tsx"], "children": []}, {"name": "MessageWrapper.tsx", "type": "file", "path": "components/ui/messages/MessageWrapper.tsx", "calls": ["components/ui/Icon.tsx", "components/ui/ProfilePhotoAvatar.tsx", "hooks/useThemeClasses.ts"], "children": []}, {"name": "TextMessage.tsx", "type": "file", "path": "components/ui/messages/TextMessage.tsx", "calls": ["components/ui/Button.tsx", "hooks/useThemeClasses.ts"], "children": []}, {"name": "index.ts", "type": "file", "path": "components/ui/messages/index.ts", "calls": ["components/ui/messages/AudioMessage.tsx", "components/ui/messages/CallingMessage.tsx", "components/ui/messages/FileMessage.tsx", "components/ui/messages/GroupUpdateMessage.tsx", "components/ui/messages/ImageMessage.tsx", "components/ui/messages/MessageWrapper.tsx", "components/ui/messages/TextMessage.tsx"], "children": []}]}]}]}, {"name": "constants", "type": "folder", "path": "constants", "calls": [], "children": [{"name": "designSystem.ts", "type": "file", "path": "constants/designSystem.ts", "calls": [], "children": []}]}, {"name": "containers", "type": "folder", "path": "containers", "calls": [], "children": [{"name": "MessageContainer.tsx", "type": "file", "path": "containers/MessageContainer.tsx", "calls": ["components/ui/index.ts", "components/ui/messages/index.ts", "contexts/AuthContext.tsx", "contexts/SocketContext.tsx", "hooks/useMessageDecryption.ts", "hooks/useThemeClasses.ts", "services/index.ts", "services/messageApi.ts", "store/index.ts", "store/slices/messageSlice.ts"], "children": []}]}, {"name": "contexts", "type": "folder", "path": "contexts", "calls": [], "children": [{"name": "AuthContext.tsx", "type": "file", "path": "contexts/AuthContext.tsx", "calls": ["services/index.ts", "types/api.ts", "types/index.ts", "utils/auth.ts"], "children": []}, {"name": "EncryptionContext.tsx", "type": "file", "path": "contexts/EncryptionContext.tsx", "calls": ["crypto/index.ts", "store/index.ts", "types/encryption.ts"], "children": []}, {"name": "SocketContext.tsx", "type": "file", "path": "contexts/SocketContext.tsx", "calls": ["contexts/AuthContext.tsx", "contexts/EncryptionContext.tsx", "services/conversationApi.ts", "services/messageApi.ts", "store/index.ts", "store/slices/callingSlice.ts", "store/slices/conversationSlice.ts", "store/slices/messageSlice.ts", "types/encryption.ts"], "children": []}, {"name": "ThemeContext.tsx", "type": "file", "path": "contexts/ThemeContext.tsx", "calls": ["constants/designSystem.ts", "utils/theme.ts"], "children": []}]}, {"name": "crypto", "type": "folder", "path": "crypto", "calls": [], "children": [{"name": "index.ts", "type": "file", "path": "crypto/index.ts", "calls": ["crypto/keyManager.ts", "crypto/keyStorage.ts", "crypto/webCrypto.ts", "types/encryption.ts"], "children": []}, {"name": "keyManager.ts", "type": "file", "path": "crypto/keyManager.ts", "calls": ["crypto/keyStorage.ts", "crypto/webCrypto.ts", "types/encryption.ts"], "children": []}, {"name": "keyStorage.ts", "type": "file", "path": "crypto/keyStorage.ts", "calls": ["crypto/webCrypto.ts", "types/encryption.ts"], "children": []}, {"name": "webCrypto.ts", "type": "file", "path": "crypto/webCrypto.ts", "calls": ["types/encryption.ts"], "children": []}]}, {"name": "hooks", "type": "folder", "path": "hooks", "calls": [], "children": [{"name": "useApiError.ts", "type": "file", "path": "hooks/useApiError.ts", "calls": ["types/index.ts"], "children": []}, {"name": "useMediaEncryption.ts", "type": "file", "path": "hooks/useMediaEncryption.ts", "calls": ["store/index.ts"], "children": []}, {"name": "useMessageDecryption.ts", "type": "file", "path": "hooks/useMessageDecryption.ts", "calls": ["contexts/EncryptionContext.tsx", "services/messageApi.ts", "store/slices/messageSlice.ts"], "children": []}, {"name": "useSocketCacheSync.ts", "type": "file", "path": "hooks/useSocketCacheSync.ts", "calls": ["contexts/SocketContext.tsx", "services/conversationApi.ts", "services/index.ts", "services/messageApi.ts", "store/index.ts"], "children": []}, {"name": "useThemeClasses.ts", "type": "file", "path": "hooks/useThemeClasses.ts", "calls": ["contexts/ThemeContext.tsx", "utils/theme.ts"], "children": []}]}, {"name": "pages", "type": "folder", "path": "pages", "calls": [], "children": [{"name": "Dashboard.tsx", "type": "file", "path": "pages/Dashboard.tsx", "calls": ["components/Chat/ChatHeader.tsx", "components/ui/SearchBar.tsx", "components/ui/index.ts", "containers/MessageContainer.tsx", "contexts/AuthContext.tsx", "contexts/SocketContext.tsx", "contexts/ThemeContext.tsx", "hooks/useThemeClasses.ts", "store/index.ts", "store/slices/conversationSlice.ts"], "children": []}, {"name": "Login.tsx", "type": "file", "path": "pages/Login.tsx", "calls": ["components/ui/Divider.tsx", "components/ui/index.ts", "contexts/ThemeContext.tsx", "hooks/useThemeClasses.ts", "services/authApi.ts", "utils/validationSchemas.ts"], "children": []}, {"name": "NewChatUI.tsx", "type": "file", "path": "pages/NewChatUI.tsx", "calls": [], "children": []}, {"name": "Register.tsx", "type": "file", "path": "pages/Register.tsx", "calls": ["components/ui/index.ts", "contexts/ThemeContext.tsx", "hooks/useThemeClasses.ts", "services/authApi.ts"], "children": []}]}, {"name": "store", "type": "folder", "path": "store", "calls": [], "children": [{"name": "index.ts", "type": "file", "path": "store/index.ts", "calls": [], "children": []}, {"name": "slices", "type": "folder", "path": "store/slices", "calls": [], "children": [{"name": "callingSlice.ts", "type": "file", "path": "store/slices/callingSlice.ts", "calls": [], "children": []}, {"name": "conversationSlice.ts", "type": "file", "path": "store/slices/conversationSlice.ts", "calls": [], "children": []}, {"name": "messageSlice.ts", "type": "file", "path": "store/slices/messageSlice.ts", "calls": [], "children": []}]}]}, {"name": "services", "type": "folder", "path": "services", "calls": [], "children": [{"name": "callingApi.ts", "type": "file", "path": "services/callingApi.ts", "calls": [], "children": []}, {"name": "mediaApi.ts", "type": "file", "path": "services/mediaApi.ts", "calls": [], "children": []}, {"name": "webrtc", "type": "folder", "path": "services/webrtc", "calls": [], "children": [{"name": "index.ts", "type": "file", "path": "services/webrtc/index.ts", "calls": [], "children": []}]}, {"name": "conversationApi.ts", "type": "file", "path": "services/conversationApi.ts", "calls": [], "children": []}, {"name": "index.ts", "type": "file", "path": "services/index.ts", "calls": [], "children": []}, {"name": "userApi.ts", "type": "file", "path": "services/userApi.ts", "calls": [], "children": []}, {"name": "messageApi.ts", "type": "file", "path": "services/messageApi.ts", "calls": [], "children": []}, {"name": "authApi.ts", "type": "file", "path": "services/authApi.ts", "calls": [], "children": []}]}, {"name": "types", "type": "folder", "path": "types", "calls": [], "children": [{"name": "api.ts", "type": "file", "path": "types/api.ts", "calls": [], "children": []}, {"name": "index.ts", "type": "file", "path": "types/index.ts", "calls": [], "children": []}, {"name": "encryption.ts", "type": "file", "path": "types/encryption.ts", "calls": [], "children": []}]}, {"name": "utils", "type": "folder", "path": "utils", "calls": [], "children": [{"name": "auth.ts", "type": "file", "path": "utils/auth.ts", "calls": [], "children": []}, {"name": "theme.ts", "type": "file", "path": "utils/theme.ts", "calls": [], "children": []}, {"name": "validationSchemas.ts", "type": "file", "path": "utils/validationSchemas.ts", "calls": [], "children": []}]}]