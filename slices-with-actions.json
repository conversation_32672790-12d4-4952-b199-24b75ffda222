{"callingSlice.ts": ["setConnectionStatus", "receiveIncomingCall", "updateCallStatus", "updateCallDuration", "startCallTimer", "setLocalStream", "setRemoteStream", "updateMediaPermissions", "setPeerConnection", "showIncomingCallModal", "hideIncomingCallModal", "showActiveCallInterface", "hideActiveCallInterface", "setCurrentCall", "setCallingError", "clearCallingError", "resetCallState"], "conversationSlice.ts": ["setSelectedConversation", "clearSelectedConversation", "updateConversationLastMessage", "addConversation", "setError", "createDraftConversation", "createDraftGroup", "removeDraftConversation", "clearDraftConversations", "convertDraftToRealConversation"], "encryptionSlice.ts": ["setKeyGenerationProgress", "setIsGeneratingKeys", "setIsUploadingKeys", "setError", "clearError", "updatePreKeyCount", "addSession", "updateSession", "removeSession", "updateOneTimePreKeys", "markPreKeysAsUploaded", "<PERSON><PERSON><PERSON><PERSON>eyAsUsed", "updateEncryptionStatus", "resetEncryptionState"], "mediaUploadSlice.ts": ["showUploadDialog", "hideUploadDialog", "showPreview", "hidePreview", "createUploadSession", "storeSessionFiles", "updateSessionMessageId", "removeFileFromSession", "updateFileStatus", "updateSessionStatus", "setFilePreview", "clearSession", "clearCompletedSessions", "retryFailedUploads"], "messageSlice.ts": ["addMessage", "addOptimisticMessage", "removeOptimisticMessage", "updateOptimisticMessage", "setTypingUsers", "addTypingUser", "removeTypingUser", "setSendingMessage", "removeSendingMessage", "clearMessages", "setError", "updateMessageStatus", "markMessageAsFailed", "retryMessage", "clearMessageError"]}