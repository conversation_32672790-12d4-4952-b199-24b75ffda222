// frontend/src/components/ui/TextField.tsx
import React, { forwardRef, useCallback, useEffect, useRef } from 'react';
import { useField, type FieldHookConfig } from 'formik';
import { Icon } from './Icon';

/**
 * TextField component sizes
 */
export type TextFieldSize = 'small' | 'medium' | 'large';

/**
 * Base props for the TextField component
 */
export interface BaseTextFieldProps extends Omit<React.TextareaHTMLAttributes<HTMLTextAreaElement>, 'size'> {
  /** Label text for the textarea */
  label?: string;
  /** Error message to display */
  error?: string | boolean;
  /** Helper text to display below the textarea */
  helperText?: string;
  /** Size of the textarea */
  size?: TextFieldSize;
  /** Whether the textarea should take full width */
  fullWidth?: boolean;
  /** Whether to show character count */
  showCharacterCount?: boolean;
  /** Maximum character count */
  maxLength?: number;
  /** Whether the textarea should auto-expand */
  autoExpand?: boolean;
  /** Minimum number of rows */
  minRows?: number;
  /** Maximum number of rows (for auto-expand) */
  maxRows?: number;
  /** Whether the textarea is resizable */
  resizable?: boolean;
}

/**
 * Props for TextField component when used with Formik
 */
export interface FormikTextFieldProps extends BaseTextFieldProps {
  /** Formik field name */
  name: string;
}

/**
 * Props for TextField component when used standalone
 */
export interface StandaloneTextFieldProps extends BaseTextFieldProps {
  name?: string;
}

export type TextFieldProps = FormikTextFieldProps | StandaloneTextFieldProps;

/**
 * Enhanced TextField component with Formik integration and auto-expanding functionality
 * 
 * Features:
 * - Seamless Formik integration with automatic error handling
 * - Auto-expanding textarea based on content
 * - Character count display with validation
 * - Resizable or fixed sizing options
 * - Glassmorphism design system integration
 * - Comprehensive accessibility support
 * 
 * @example
 * ```tsx
 * // With Formik and auto-expand
 * <TextField 
 *   name="message" 
 *   label="Message" 
 *   autoExpand 
 *   maxRows={10}
 *   showCharacterCount
 *   maxLength={500}
 * />
 * 
 * // Standalone with fixed size
 * <TextField 
 *   label="Description" 
 *   rows={4}
 *   resizable={false}
 *   placeholder="Enter description..."
 * />
 * ```
 */
export const TextField = forwardRef<HTMLTextAreaElement, TextFieldProps>(({
  label,
  error: errorProp,
  helperText,
  size = 'medium',
  fullWidth = true,
  showCharacterCount = false,
  maxLength,
  autoExpand = false,
  minRows = 3,
  maxRows = 10,
  resizable = true,
  className = '',
  disabled,
  rows: rowsProp,
  ...props
}, ref) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const combinedRef = ref || textareaRef;
  
  // Check if this is a Formik field
  const isFormikField = 'name' in props && props.name;
  
  // Use Formik field if name is provided
  const [field, meta] = isFormikField 
    ? useField(props as FieldHookConfig<string>)
    : [null, null];

  // Determine error state
  const hasError = errorProp || (meta?.touched && meta?.error);
  const errorMessage = typeof errorProp === 'string' ? errorProp : meta?.error;

  // Auto-expand functionality
  const adjustHeight = useCallback(() => {
    const textarea = typeof combinedRef === 'function' ? null : combinedRef?.current;
    if (!textarea || !autoExpand) return;

    // Reset height to auto to get the correct scrollHeight
    textarea.style.height = 'auto';
    
    // Calculate new height based on content
    const scrollHeight = textarea.scrollHeight;
    const lineHeight = parseInt(getComputedStyle(textarea).lineHeight);
    const minHeight = lineHeight * minRows;
    const maxHeight = lineHeight * maxRows;
    
    const newHeight = Math.min(Math.max(scrollHeight, minHeight), maxHeight);
    textarea.style.height = `${newHeight}px`;
  }, [autoExpand, minRows, maxRows, combinedRef]);

  // Adjust height when value changes
  useEffect(() => {
    adjustHeight();
  }, [field?.value, props.value, adjustHeight]);

  // Adjust height on mount
  useEffect(() => {
    adjustHeight();
  }, [adjustHeight]);

  // Size-specific classes
  const sizeClasses = {
    small: 'px-3 py-1.5 text-sm rounded-md',
    medium: 'px-3 py-2 text-sm rounded-lg',
    large: 'px-4 py-3 text-base rounded-xl',
  };

  // Base textarea classes with glassmorphism
  const baseClasses = [
    'block w-full transition-all duration-250',
    'glass-light border-border-glass',
    'text-text-primary placeholder-text-tertiary',
    'focus:border-border-focus focus:ring-2 focus:ring-primary/20',
    'focus:outline-none focus:bg-bg-primary/50',
    'disabled:opacity-50 disabled:cursor-not-allowed',
    sizeClasses[size],
    resizable ? 'resize-y' : 'resize-none',
  ].join(' ');

  // Error state classes
  const errorClasses = hasError 
    ? 'border-danger focus:border-danger focus:ring-danger/20' 
    : '';

  const finalTextareaClasses = `${baseClasses} ${errorClasses} ${className}`;

  // Character count
  const currentLength = field?.value?.length || props.value?.toString()?.length || 0;

  // Handle input change for auto-expand
  const handleChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    // Call original onChange if provided
    if (field?.onChange) {
      field.onChange(e);
    } else if (props.onChange) {
      props.onChange(e);
    }
    
    // Adjust height after state update
    setTimeout(adjustHeight, 0);
  }, [field?.onChange, props.onChange, adjustHeight]);

  // Determine rows
  const finalRows = autoExpand ? minRows : (rowsProp || minRows);

  return (
    <div className={`space-y-1 ${fullWidth ? 'w-full' : ''}`}>
      {/* Label */}
      {label && (
        <label 
          htmlFor={props.id || props.name} 
          className="block text-sm font-medium text-text-primary"
        >
          {label}
          {props.required && <span className="text-danger ml-1">*</span>}
        </label>
      )}

      {/* Textarea */}
      <textarea
        ref={combinedRef}
        className={finalTextareaClasses}
        disabled={disabled}
        maxLength={maxLength}
        rows={finalRows}
        {...(field ? { ...field, onChange: handleChange } : { onChange: handleChange })}
        {...(field ? { ...field, onChange: undefined } : {})}
        {...props}
      />

      {/* Helper text, error message, and character count */}
      <div className="flex justify-between items-start">
        <div className="flex-1">
          {/* Error message */}
          {hasError && errorMessage && (
            <p className="text-sm text-danger flex items-center">
              <Icon name="alert" className="mr-1 flex-shrink-0" size={14} />
              {errorMessage}
            </p>
          )}
          
          {/* Helper text */}
          {!hasError && helperText && (
            <p className="text-sm text-text-secondary">
              {helperText}
            </p>
          )}
        </div>

        {/* Character count */}
        {showCharacterCount && maxLength && (
          <p className={`text-xs ml-2 flex-shrink-0 ${
            currentLength > maxLength ? 'text-danger' : 'text-text-tertiary'
          }`}>
            {currentLength}/{maxLength}
          </p>
        )}
      </div>
    </div>
  );
});

TextField.displayName = 'TextField';
