@import "tailwindcss";
@import "./styles/calling.css";

/* Design System CSS Custom Properties */
:root {
  /* Typography */
  --font-family-primary: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  --font-family-secondary: <PERSON><PERSON>s, Inter, system-ui, sans-serif;
  --line-height-base: 1.5;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Spacing Scale */
  --spacing-xs: 0.25rem;    /* 4px */
  --spacing-sm: 0.5rem;     /* 8px */
  --spacing-md: 0.75rem;    /* 12px */
  --spacing-lg: 1rem;       /* 16px */
  --spacing-xl: 1.5rem;     /* 24px */
  --spacing-2xl: 2rem;      /* 32px */
  --spacing-3xl: 3rem;      /* 48px */

  /* Border Radius */
  --radius-sm: 8px;         /* inputs */
  --radius-md: 12px;        /* buttons */
  --radius-lg: 16px;        /* cards */
  --radius-xl: 20px;        /* modals */
  --radius-full: 9999px;    /* pills */

  /* Glassmorphism Effects */
  --glass-blur: 12px;
  --glass-blur-sm: 8px;
  --glass-blur-lg: 16px;
  --glass-opacity: 0.2;
  --glass-opacity-light: 0.1;
  --glass-opacity-heavy: 0.3;

  /* Light Theme Colors */
  --color-primary: #3b82f6;
  --color-primary-hover: #2563eb;
  --color-primary-light: #dbeafe;
  --color-secondary: #6b7280;
  --color-secondary-hover: #4b5563;
  --color-danger: #ef4444;
  --color-danger-hover: #dc2626;
  --color-success: #10b981;
  --color-warning: #f59e0b;

  /* Background Colors - Light Theme */
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-tertiary: #f3f4f6;
  --bg-glass: #e5e7eb;
  --bg-glass-light: #f3f4f6;
  --bg-glass-heavy: #d1d5db;
  --bg-overlay: #1f2937;

  /* Text Colors - Light Theme */
  --text-primary: #111827;
  --text-secondary: #6b7280;
  --text-tertiary: #9ca3af;
  --text-inverse: #ffffff;
  --text-muted: #d1d5db;

  /* Border Colors - Light Theme */
  --border-primary: #e5e7eb;
  --border-secondary: #d1d5db;
  --border-glass: #d1d5db;
  --border-focus: #3b82f6;

  /* Shadow */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-glass: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
}

/* Dark Theme */
[data-theme="dark"] {
  /* Background Colors - Dark Theme */
  --bg-primary: #1f2937;
  --bg-secondary: #111827;
  --bg-tertiary: #374151;
  --bg-glass: #374151;
  --bg-glass-light: #4b5563;
  --bg-glass-heavy: #1f2937;
  --bg-overlay: #111827;

  /* Text Colors - Dark Theme */
  --text-primary: #f9fafb;
  --text-secondary: #d1d5db;
  --text-tertiary: #9ca3af;
  --text-inverse: #111827;
  --text-muted: #6b7280;

  /* Border Colors - Dark Theme */
  --border-primary: #374151;
  --border-secondary: #4b5563;
  --border-glass: #4b5563;

  /* Gradients - Dark Theme */
  --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
}

/* Base Styles */
body {
  margin: 0;
  min-height: 100vh;
  font-family: var(--font-family-primary);
  line-height: var(--line-height-base);
  font-weight: var(--font-weight-normal);
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

* {
  box-sizing: border-box;
}

/* Glassmorphism Utility Classes */
.glass {
  background: var(--bg-glass);
  backdrop-filter: blur(var(--glass-blur));
  -webkit-backdrop-filter: blur(var(--glass-blur));
  border: 1px solid var(--border-glass);
}

.glass-light {
  background: var(--bg-glass-light);
  backdrop-filter: blur(var(--glass-blur-sm));
  -webkit-backdrop-filter: blur(var(--glass-blur-sm));
  border: 1px solid var(--border-glass);
}

.glass-heavy {
  background: var(--bg-glass-heavy);
  backdrop-filter: blur(var(--glass-blur-lg));
  -webkit-backdrop-filter: blur(var(--glass-blur-lg));
  border: 1px solid var(--border-glass);
}

/* Smooth transitions for theme switching */
* {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}
