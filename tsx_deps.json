{"App.tsx": ["components/calling/index.ts", "components/ui/ErrorBoundary.tsx", "contexts/AuthContext.tsx", "contexts/EncryptionContext.tsx", "contexts/SocketContext.tsx", "contexts/ThemeContext.tsx", "hooks/useSocketCacheSync.ts", "pages/Dashboard.tsx", "pages/Login.tsx", "pages/NewChatUI.tsx", "pages/Register.tsx", "store/index.ts"], "components/Chat/ChatHeader.tsx": ["components/ui/ProfilePhotoAvatar.tsx", "contexts/SocketContext.tsx", "hooks/useThemeClasses.ts", "services/callingApi.ts", "store/index.ts", "store/slices/callingSlice.ts", "store/slices/conversationSlice.ts"], "components/Chat/SimpleMediaUpload.tsx": ["contexts/SocketContext.tsx", "hooks/useMediaEncryption.ts", "services/mediaApi.ts"], "components/Icons/Google.tsx": [], "components/calling/ActiveCallInterface.tsx": ["components/calling/CallControls.tsx", "components/calling/CallStatusIndicator.tsx", "components/calling/VideoContainer.tsx", "store/slices/callingSlice.ts"], "components/calling/CallControls.tsx": ["contexts/SocketContext.tsx", "services/callingApi.ts", "store/index.ts", "store/slices/callingSlice.ts"], "components/calling/CallManager.tsx": ["components/calling/ActiveCallInterface.tsx", "components/calling/IncomingCallModal.tsx", "contexts/SocketContext.tsx", "services/webrtc/index.ts", "store/index.ts", "store/slices/callingSlice.ts"], "components/calling/CallStatusIndicator.tsx": ["store/slices/callingSlice.ts"], "components/calling/IncomingCallModal.tsx": ["services/callingApi.ts", "store/slices/callingSlice.ts"], "components/calling/VideoContainer.tsx": ["store/slices/callingSlice.ts"], "components/calling/index.ts": ["components/calling/ActiveCallInterface.tsx", "components/calling/CallControls.tsx", "components/calling/CallManager.tsx", "components/calling/CallStatusIndicator.tsx", "components/calling/IncomingCallModal.tsx", "components/calling/VideoContainer.tsx"], "components/ui/ApiErrorDisplay.tsx": ["components/ui/Button.tsx", "components/ui/Icon.tsx", "hooks/useApiError.ts"], "components/ui/Button.tsx": ["components/ui/Icon.tsx", "constants/designSystem.ts"], "components/ui/ConversationItem.tsx": ["components/ui/Icon.tsx", "components/ui/ProfilePhotoAvatar.tsx"], "components/ui/Divider.tsx": [], "components/ui/ErrorBoundary.tsx": ["components/ui/Button.tsx", "components/ui/Icon.tsx"], "components/ui/Form.tsx": ["components/ui/Button.tsx", "components/ui/Icon.tsx"], "components/ui/Icon.tsx": ["components/Icons/Google.tsx"], "components/ui/Input.tsx": ["components/ui/Icon.tsx", "constants/designSystem.ts"], "components/ui/LoadingSpinner.tsx": ["components/ui/Icon.tsx"], "components/ui/MessageInput.tsx": ["components/Chat/SimpleMediaUpload.tsx", "components/ui/Button.tsx", "components/ui/Icon.tsx", "components/ui/TextField.tsx", "contexts/SocketContext.tsx", "store/index.ts"], "components/ui/ProfilePhotoAvatar.tsx": ["components/ui/Icon.tsx", "constants/designSystem.ts"], "components/ui/SearchBar.tsx": ["components/ui/Icon.tsx"], "components/ui/Select.tsx": ["components/ui/Icon.tsx"], "components/ui/Sidebar.tsx": ["components/ui/Button.tsx", "components/ui/ConversationItem.tsx", "components/ui/Icon.tsx", "components/ui/SearchBar.tsx", "components/ui/ThemeToggle.tsx", "contexts/AuthContext.tsx", "hooks/useThemeClasses.ts", "services/conversationApi.ts", "services/index.ts", "services/userApi.ts"], "components/ui/TextField.tsx": ["components/ui/Icon.tsx"], "components/ui/ThemeToggle.tsx": ["components/ui/Icon.tsx", "constants/designSystem.ts", "contexts/ThemeContext.tsx", "hooks/useThemeClasses.ts"], "components/ui/index.ts": ["components/ui/ApiErrorDisplay.tsx", "components/ui/Button.tsx", "components/ui/ConversationItem.tsx", "components/ui/Divider.tsx", "components/ui/ErrorBoundary.tsx", "components/ui/Form.tsx", "components/ui/Icon.tsx", "components/ui/Input.tsx", "components/ui/LoadingSpinner.tsx", "components/ui/MessageInput.tsx", "components/ui/ProfilePhotoAvatar.tsx", "components/ui/SearchBar.tsx", "components/ui/Select.tsx", "components/ui/Sidebar.tsx", "components/ui/TextField.tsx", "components/ui/ThemeToggle.tsx", "components/ui/messages/index.ts", "constants/designSystem.ts"], "components/ui/messages/AudioMessage.tsx": ["components/ui/Icon.tsx"], "components/ui/messages/CallingMessage.tsx": ["components/ui/Icon.tsx"], "components/ui/messages/FileMessage.tsx": ["components/ui/Icon.tsx"], "components/ui/messages/GroupUpdateMessage.tsx": ["components/ui/Icon.tsx", "components/ui/ProfilePhotoAvatar.tsx"], "components/ui/messages/ImageMessage.tsx": ["components/ui/Icon.tsx"], "components/ui/messages/MessageWrapper.tsx": ["components/ui/Icon.tsx", "components/ui/ProfilePhotoAvatar.tsx", "hooks/useThemeClasses.ts"], "components/ui/messages/TextMessage.tsx": ["components/ui/Button.tsx", "hooks/useThemeClasses.ts"], "components/ui/messages/index.ts": ["components/ui/messages/AudioMessage.tsx", "components/ui/messages/CallingMessage.tsx", "components/ui/messages/FileMessage.tsx", "components/ui/messages/GroupUpdateMessage.tsx", "components/ui/messages/ImageMessage.tsx", "components/ui/messages/MessageWrapper.tsx", "components/ui/messages/TextMessage.tsx"], "constants/designSystem.ts": [], "containers/MessageContainer.tsx": ["components/ui/index.ts", "components/ui/messages/index.ts", "contexts/AuthContext.tsx", "contexts/SocketContext.tsx", "hooks/useMessageDecryption.ts", "hooks/useThemeClasses.ts", "services/index.ts", "services/messageApi.ts", "store/index.ts", "store/slices/messageSlice.ts"], "contexts/AuthContext.tsx": ["services/index.ts", "types/api.ts", "types/index.ts", "utils/auth.ts"], "contexts/EncryptionContext.tsx": ["crypto/index.ts", "store/index.ts", "types/encryption.ts"], "contexts/SocketContext.tsx": ["contexts/AuthContext.tsx", "contexts/EncryptionContext.tsx", "services/conversationApi.ts", "services/messageApi.ts", "store/index.ts", "store/slices/callingSlice.ts", "store/slices/conversationSlice.ts", "store/slices/messageSlice.ts", "types/encryption.ts"], "contexts/ThemeContext.tsx": ["constants/designSystem.ts", "utils/theme.ts"], "crypto/index.ts": ["crypto/keyManager.ts", "crypto/keyStorage.ts", "crypto/webCrypto.ts", "types/encryption.ts"], "crypto/keyManager.ts": ["crypto/keyStorage.ts", "crypto/webCrypto.ts", "types/encryption.ts"], "crypto/keyStorage.ts": ["crypto/webCrypto.ts", "types/encryption.ts"], "crypto/webCrypto.ts": ["types/encryption.ts"], "hooks/useApiError.ts": ["types/index.ts"], "hooks/useMediaEncryption.ts": ["store/index.ts"], "hooks/useMessageDecryption.ts": ["contexts/EncryptionContext.tsx", "services/messageApi.ts", "store/slices/messageSlice.ts"], "hooks/useSocketCacheSync.ts": ["contexts/SocketContext.tsx", "services/conversationApi.ts", "services/index.ts", "services/messageApi.ts", "store/index.ts"], "hooks/useThemeClasses.ts": ["contexts/ThemeContext.tsx", "utils/theme.ts"], "pages/Dashboard.tsx": ["components/Chat/ChatHeader.tsx", "components/ui/SearchBar.tsx", "components/ui/index.ts", "containers/MessageContainer.tsx", "contexts/AuthContext.tsx", "contexts/SocketContext.tsx", "contexts/ThemeContext.tsx", "hooks/useThemeClasses.ts", "store/index.ts", "store/slices/conversationSlice.ts"], "pages/Login.tsx": ["components/ui/Divider.tsx", "components/ui/index.ts", "contexts/ThemeContext.tsx", "hooks/useThemeClasses.ts", "services/authApi.ts", "utils/validationSchemas.ts"], "pages/NewChatUI.tsx": [], "pages/Register.tsx": ["components/ui/index.ts", "contexts/ThemeContext.tsx", "hooks/useThemeClasses.ts", "services/authApi.ts"]}