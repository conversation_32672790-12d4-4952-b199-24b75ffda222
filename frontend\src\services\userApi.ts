// frontend/src/services/userApi.ts
import { api } from './api';
import type { SearchUsersRequest, SearchUsersResponse } from '../types';

export interface SearchUser {
  id: string;
  name: string;
  profile_picture?: string;
}

export const userApi = api.injectEndpoints({
  endpoints: (builder) => ({
    searchUsers: builder.query<SearchUsersResponse, string>({
      query: (query) => ({
        url: '/messaging/users/search/',
        params: { q: query },
      }),
      providesTags: (result, error, query) => [
        { type: 'User', id: `SEARCH-${query}` },
      ],
      // Cache results for 5 minutes
      keepUnusedDataFor: 300,
      transformResponse: (response: any): SearchUsersResponse => {
        // Handle the actual API response structure which uses 'results' field
        return {
          success: response.success || true,
          results: response.results || response.data || [],
        };
      },
      transformErrorResponse: (response: any) => ({
        status: response.status,
        data: response.data || { 
          success: false, 
          error: 'Failed to search users', 
          timestamp: new Date().toISOString() 
        }
      }),
    }),

    // Get user profile by ID
    getUserProfile: builder.query<{ success: boolean; data: SearchUser }, string>({
      query: (userId) => `/messaging/users/${userId}/`,
      providesTags: (result, error, userId) => [
        { type: 'User', id: userId },
      ],
      transformErrorResponse: (response: any) => ({
        status: response.status,
        data: response.data || { 
          success: false, 
          error: 'Failed to fetch user profile', 
          timestamp: new Date().toISOString() 
        }
      }),
    }),



    // Update current user's profile
    updateUserProfile: builder.mutation<
      { success: boolean; data: SearchUser }, 
      Partial<Pick<SearchUser, 'first_name' | 'last_name' | 'username'>>
    >({
      query: (profileData) => ({
        url: '/auth/profile/',
        method: 'PATCH',
        body: profileData,
      }),
      invalidatesTags: ['User', 'Auth'],
      transformErrorResponse: (response: any) => ({
        status: response.status,
        data: response.data || { 
          success: false, 
          error: 'Failed to update profile', 
          timestamp: new Date().toISOString() 
        }
      }),
    }),
  }),
  overrideExisting: false,
});

export const {
  useSearchUsersQuery,
  useLazySearchUsersQuery,
  useGetUserProfileQuery,
  useLazyGetUserProfileQuery,
  useUpdateUserProfileMutation,
} = userApi;
