// frontend/src/pages/Dashboard.tsx
import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Button, Icon, ProfilePhotoAvatar } from '../components/ui';
import { useAuth } from '../contexts/AuthContext';
import { useSocket } from '../contexts/SocketContext';
import { useTheme } from '../contexts/ThemeContext';
import type { RootState, AppDispatch } from '../store';
import { Sidebar } from '../components/ui';
import { ChatHeader } from '../components/Chat/ChatHeader';
import { MessageContainer } from '../containers/MessageContainer';
import { setSelectedConversation, createDraftConversation } from '../store/slices/conversationSlice';
import type { SearchResult } from '../components/ui/SearchBar';
import { useThemeClasses } from '../hooks/useThemeClasses';

export const Dashboard: React.FC = () => {
  const { user, logout } = useAuth();
  const { isConnected } = useSocket();
  const { effectiveTheme, toggleTheme } = useTheme();
  const dispatch = useDispatch<AppDispatch>();
  const selectedConversationId = useSelector((state: RootState) => state.conversations.selectedConversationId);
  const { classes, isDark } = useThemeClasses();

  const handleLogout = () => {
    logout();
  };

  // Handle conversation selection from sidebar
  const handleConversationSelect = (conversationId: string) => {
    dispatch(setSelectedConversation(conversationId));
  };

  // Handle search result selection from sidebar
  const handleSearchResultSelect = (result: SearchResult) => {
    if (result.type === 'user') {
      // Create a draft conversation with the selected user
      dispatch(createDraftConversation({
        userId: result.id,
        username: result.subtitle?.replace('@', '') || result.title, // Extract username from subtitle
        first_name: result.title.split(' ')[0] || result.title,
        last_name: result.title.split(' ').slice(1).join(' ') || '',
        profile_picture: result.avatar || null
      }));
    }
  };

  // Handle new conversation button click
  const handleNewConversation = () => {
    // This will be handled by the search functionality in the sidebar
    // The user can search for users and create new conversations
  };

  return (
    <div className={`flex h-screen ${classes.layout.main}`}>
      {/* Left Sidebar */}
      <div className={`w-96 flex flex-col ${classes.layout.sidebar}`}>
        {/* Sidebar Header */}
        <div className={`p-4 border-b ${classes.border.primary}`}>
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-teal-500 rounded-full flex items-center justify-center">
                <Icon name="message" className="text-white" size={20} />
              </div>
              <div>
                <h2 className={`font-semibold ${classes.text.primary}`}>{user?.name || 'User'}</h2>
                <p className={`text-sm ${classes.text.secondary}`}>My Account</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={toggleTheme}
                className={`w-8 h-8 flex items-center justify-center rounded-full transition-colors ${
                  isDark
                    ? 'text-gray-400 hover:text-white hover:bg-gray-700'
                    : 'text-gray-500 hover:text-gray-900 hover:bg-gray-100'
                }`}
                title={`Switch to ${effectiveTheme === 'dark' ? 'light' : 'dark'} mode`}
              >
                <Icon name={effectiveTheme === 'dark' ? 'sun' : 'moon'} size={16} />
              </button>
              <button
                onClick={handleLogout}
                className={`w-8 h-8 flex items-center justify-center rounded-full transition-colors ${
                  isDark
                    ? 'text-gray-400 hover:text-white hover:bg-gray-700'
                    : 'text-gray-500 hover:text-gray-900 hover:bg-gray-100'
                }`}
                title="Logout"
                data-testid="logout-button"
              >
                <Icon name="more-horizontal" size={16} />
              </button>
            </div>
          </div>
          {/* Search Bar */}
          <div className="relative">
            <Icon name="search" className={`absolute left-3 top-1/2 transform -translate-y-1/2 ${
              isDark ? 'text-gray-500' : 'text-gray-400'
            }`} size={16} />
            <input
              placeholder="Search or start new chat..."
              className={`w-full pl-10 pr-4 py-2 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-teal-500 ${
                isDark
                  ? 'bg-gray-700 text-white placeholder-gray-500'
                  : 'bg-gray-100 text-gray-900 placeholder-gray-400'
              }`}
              type="text"
              value=""
            />
          </div>
        </div>

        {/* Conversations List */}
        <div className="flex-1 overflow-y-auto">
          <div className="p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className={`font-medium ${classes.text.primary}`}>Messages</h3>
              <span className="bg-teal-500 text-white text-xs px-2 py-1 rounded-full">
                {/* Message count will be handled by Sidebar component */}
              </span>
            </div>
            <div className="space-y-2">
              <Sidebar
                selectedConversationId={selectedConversationId}
                onConversationSelect={handleConversationSelect}
                onSearchResultSelect={handleSearchResultSelect}
                onNewConversation={handleNewConversation}
              />
            </div>
          </div>
        </div>

        {/* Bottom Navigation */}
        <div className={`p-4 border-t ${classes.border.primary}`}>
          <div className="flex justify-around">
            <button className={`w-10 h-10 flex items-center justify-center transition-colors ${
              isDark
                ? 'text-gray-400 hover:text-white'
                : 'text-gray-500 hover:text-gray-900'
            }`}>
              <Icon name="user" size={20} />
            </button>
            <button className={`w-10 h-10 flex items-center justify-center transition-colors ${
              isDark
                ? 'text-gray-400 hover:text-white'
                : 'text-gray-500 hover:text-gray-900'
            }`}>
              <Icon name="phone" size={20} />
            </button>
            <button className={`w-10 h-10 flex items-center justify-center transition-colors ${
              isDark
                ? 'text-gray-400 hover:text-white'
                : 'text-gray-500 hover:text-gray-900'
            }`}>
              <Icon name="chart-area" size={20} />
            </button>
            <button className={`w-10 h-10 flex items-center justify-center transition-colors ${
              isDark
                ? 'text-gray-400 hover:text-white'
                : 'text-gray-500 hover:text-gray-900'
            }`}>
              <Icon name="bookmark" size={20} />
            </button>
            <button className={`w-10 h-10 flex items-center justify-center transition-colors ${
              isDark
                ? 'text-gray-400 hover:text-white'
                : 'text-gray-500 hover:text-gray-900'
            }`}>
              <Icon name="settings" size={20} />
            </button>
          </div>
        </div>
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex">
        <div className={`flex-1 flex flex-col ${classes.layout.chat}`} data-testid="chat-area">
          {selectedConversationId ? (
            <>
              {/* Chat Header */}
              <ChatHeader
                conversationId={selectedConversationId}
                currentUserId={user?.id || ''}
              />

              {/* Message Container */}
              <MessageContainer conversationId={selectedConversationId} />
            </>
          ) : (
            <div className={`flex-1 flex items-center justify-center ${classes.layout.chat}`}>
              <div className="text-center">
                <Icon name="message-circle" className={`mx-auto ${classes.text.tertiary}`} size={48} />
                <h3 className={`mt-2 text-sm font-medium ${classes.text.primary}`}>No conversation selected</h3>
                <p className={`mt-1 text-sm ${classes.text.secondary}`}>
                  Choose a conversation from the sidebar to start messaging
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

    </div>
  );
};
