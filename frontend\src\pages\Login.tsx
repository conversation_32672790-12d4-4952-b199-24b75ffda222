// frontend/src/pages/Login.tsx
import React, { useEffect, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Form, Input, Button, Icon } from '../components/ui';
import { useTheme } from '../contexts/ThemeContext';
import { useLoginMutation, useRequestLoginOTPMutation, useVerifyLoginOTPMutation } from '../services/authApi';
import { loginSchema } from '../utils/validationSchemas';
import { Divider } from '../components/ui/Divider';
import * as Yup from 'yup';

interface LoginForm {
  email: string;
  password: string;
}

interface OTPLoginForm {
  email: string;
}

interface OTPVerifyForm {
  otpCode: string;
}

// Validation schema for OTP login email
const otpEmailSchema = Yup.object().shape({
  email: Yup.string()
    .email('Please enter a valid email address')
    .required('Email is required'),
});

// Validation schema for OTP verification
const otpVerifySchema = Yup.object().shape({
  otpCode: Yup.string()
    .length(6, 'OTP must be 6 digits')
    .matches(/^\d{6}$/, 'OTP must contain only numbers')
    .required('OTP is required'),
});

const Login: React.FC = () => {
  const { effectiveTheme, toggleTheme } = useTheme();
  const [loginMutation, { isLoading, error, isSuccess, data }] = useLoginMutation();
  const [requestOTPMutation, { isLoading: isRequestingOTP, error: requestOTPError }] = useRequestLoginOTPMutation();
  const [verifyOTPMutation, { isLoading: isVerifyingOTP, error: verifyOTPError }] = useVerifyLoginOTPMutation();
  const navigate = useNavigate();

  // Login mode state
  const [loginMode, setLoginMode] = useState<'password' | 'otp'>('password');
  const [otpStep, setOtpStep] = useState<'email' | 'verify'>('email');
  const [otpEmail, setOtpEmail] = useState('');

  // Handle successful login
  useEffect(() => {
    if (isSuccess && data?.user) {
      // Tokens are already stored by the API transformation in authApi.ts
      // Navigate to dashboard
      navigate('/dashboard');
    }
  }, [isSuccess, data, navigate]);

  const handleLoginSubmit = async (values: LoginForm) => {
    // RTK Query handles the mutation automatically
    await loginMutation(values);
  };

  // OTP Login handlers
  const handleOTPEmailSubmit = async (values: OTPLoginForm) => {
    try {
      const result = await requestOTPMutation({ email: values.email }).unwrap();
      if (result.success) {
        setOtpEmail(values.email);
        setOtpStep('verify');
      }
    } catch (error) {
      console.error('OTP request error:', error);
    }
  };

  const handleOTPVerifySubmit = async (values: OTPVerifyForm) => {
    try {
      const result = await verifyOTPMutation({
        email: otpEmail,
        otpCode: values.otpCode,
      }).unwrap();

      if (result.success && result.data) {
        // Navigate to dashboard
        navigate('/dashboard');
      }
    } catch (error) {
      console.error('OTP verification error:', error);
    }
  };

  const handleSwitchToOTPLogin = () => {
    setLoginMode('otp');
    setOtpStep('email');
    setOtpEmail('');
  };

  const handleSwitchToPasswordLogin = () => {
    setLoginMode('password');
    setOtpStep('email');
    setOtpEmail('');
  };

  return (
    <div className="min-h-screen bg-gray-900 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        {/* Header */}
        <div className="flex justify-center">
          <div className="w-12 h-12 bg-teal-500 rounded-lg flex items-center justify-center shadow-lg">
            <Icon name="message" className="text-white" size={24} />
          </div>
        </div>
        <h2 className="mt-6 text-center text-3xl font-bold tracking-tight text-white">
          {loginMode === 'password'
            ? 'Sign in to your account'
            : otpStep === 'email'
              ? 'Login with Email OTP'
              : 'Verify your email'
          }
        </h2>
        <p className="mt-2 text-center text-sm text-gray-400">
          {loginMode === 'password' ? (
            <>
              Don't have an account?{' '}
              <Link
                to="/register"
                className="font-medium text-teal-400 hover:text-teal-300 transition-colors"
              >
                Sign up
              </Link>
            </>
          ) : otpStep === 'email' ? (
            <>
              Want to use password instead?{' '}
              <button
                type="button"
                onClick={handleSwitchToPasswordLogin}
                className="font-medium text-teal-400 hover:text-teal-300 transition-colors"
              >
                Password Login
              </button>
            </>
          ) : (
            <>
              We sent a 6-digit code to <strong className="text-white">{otpEmail}</strong>
            </>
          )}
        </p>

        {/* Theme Toggle */}
        <div className="flex justify-center mt-4">
          <Button
            variant="ghost"
            icon={effectiveTheme === 'dark' ? 'sun' : 'moon'}
            onClick={toggleTheme}
            size="small"
          >
            {effectiveTheme === 'dark' ? 'Light' : 'Dark'} Mode
          </Button>
        </div>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-gray-800 rounded-xl shadow-xl p-8 border border-gray-700">
          {loginMode === 'password' ? (
            /* Password Login Form */
            <>
              <Form
                initialValues={{
                  email: '',
                  password: '',
                }}
                validationSchema={loginSchema}
                onSubmit={handleLoginSubmit}
                showSubmitButton={false} // Disable default submit button
                loading={isLoading}
                errorComponent={() => (
                  <div className="rounded-lg bg-red-900/20 border border-red-700 p-4">
                    <div className="flex items-start">
                      <Icon name="alert" className="text-red-400 mt-0.5 flex-shrink-0" size={16} />
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-red-400">
                          Login Error
                        </h3>
                        <div className="mt-1 text-sm text-red-300">
                          <p>{(error as any)?.data?.error || 'Login failed. Please check your credentials and try again.'}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
                showFormErrors={!!error}
              >
                {(formik) => (
                  <>
                    {/* Email Field */}
                    <Input
                      name="email"
                      type="email"
                      label="Email Address"
                      placeholder="Enter your email address"
                      required
                      data-testid="email-input"
                    />

                    {/* Password Field */}
                    <div className="space-y-1">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium text-white">Password</span>
                        <Link
                          to="/forgot-password"
                          className="text-sm text-teal-400 hover:text-teal-300 transition-colors"
                        >
                          Forgot password?
                        </Link>
                      </div>
                      <Input
                        name="password"
                        type="password"
                        placeholder="Enter your password"
                        required
                        data-testid="password-input"
                      />
                    </div>

                    {/* Custom Sign In Button */}
                    <Button
                      type="submit"
                      variant="outline"
                      fullWidth
                      loading={isLoading}
                      disabled={isLoading || !formik.isValid}
                      className="mt-4"
                    >
                      {isLoading ? 'Signing in...' : 'Sign in'}
                    </Button>
                  </>
                )}
              </Form>

              {/* Divider */}
              <Divider text="OR" />

              {/* Alternative Login Methods */}
              <div className="mt-6 space-y-3">
                {/* Email OTP Login Button */}
                <Button
                  variant="outline"
                  fullWidth
                  className="justify-center"
                  icon="mail"
                  iconPosition="left"
                  onClick={handleSwitchToOTPLogin}
                >
                  Login with Email OTP
                </Button>

                {/* Google Login Button */}
                <Button
                  variant="outline"
                  fullWidth
                  className="justify-center"
                  icon="google"
                  iconPosition="left"
                >
                  Continue with Google
                </Button>
              </div>
            </>
          ) : otpStep === 'email' ? (
            /* OTP Email Input Form */
            <Form
              initialValues={{ email: '' }}
              validationSchema={otpEmailSchema}
              onSubmit={handleOTPEmailSubmit}
              submitButtonText="Send Verification Code"
              loading={isRequestingOTP}
              errorComponent={() => (
                <div className="rounded-lg bg-red-900/20 border border-red-700 p-4">
                  <div className="flex items-start">
                    <Icon name="alert" className="text-red-400 mt-0.5 flex-shrink-0" size={16} />
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-red-400">
                        Error
                      </h3>
                      <div className="mt-1 text-sm text-red-300">
                        <p>{(requestOTPError as any)?.data?.error || 'Failed to send verification code. Please try again.'}</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              showFormErrors={!!requestOTPError}
            >
              <Input
                name="email"
                type="email"
                label="Email Address"
                placeholder="Enter your email address"
                required
                data-testid="email-input"
              />
            </Form>
          ) : (
            /* OTP Verification Form */
            <div className="space-y-6">
              <div className="text-center">
                <p className="text-sm text-gray-400 mb-4">
                  Enter the 6-digit code we sent to your email
                </p>
                <Button
                  variant="ghost"
                  size="small"
                  onClick={() => setOtpStep('email')}
                  className="text-teal-400 hover:text-teal-300"
                >
                  ← Change email address
                </Button>
              </div>

              <Form
                initialValues={{ otpCode: '' }}
                validationSchema={otpVerifySchema}
                onSubmit={handleOTPVerifySubmit}
                submitButtonText="Verify & Login"
                loading={isVerifyingOTP}
                errorComponent={() => (
                  <div className="rounded-lg bg-red-900/20 border border-red-700 p-4">
                    <div className="flex items-start">
                      <Icon name="alert" className="text-red-400 mt-0.5 flex-shrink-0" size={16} />
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-red-400">
                          Verification Error
                        </h3>
                        <div className="mt-1 text-sm text-red-300">
                          <p>{(verifyOTPError as any)?.data?.error || 'Invalid verification code. Please try again.'}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
                showFormErrors={!!verifyOTPError}
              >
                <Input
                  name="otpCode"
                  type="text"
                  label="Verification Code"
                  placeholder="Enter 6-digit code"
                  maxLength={6}
                  required
                  data-testid="otp-input"
                  className="text-center text-lg tracking-widest"
                />
              </Form>

              {/* Resend Code */}
              <div className="text-center">
                <p className="text-sm text-gray-400">
                  Didn't receive the code?{' '}
                  <button
                    type="button"
                    onClick={() => handleOTPEmailSubmit({ email: otpEmail })}
                    disabled={isRequestingOTP}
                    className="font-medium text-teal-400 hover:text-teal-300 transition-colors disabled:opacity-50"
                  >
                    {isRequestingOTP ? 'Sending...' : 'Resend code'}
                  </button>
                </p>
              </div>
            </div>
          )}
        </div>
      </div>


    </div>
  );
};

export default Login;
