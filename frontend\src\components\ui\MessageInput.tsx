// frontend/src/components/ui/MessageInput.tsx
import React, { useState, useRef, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useSocket } from '../../contexts/SocketContext';
import SimpleMediaUpload from '../Chat/SimpleMediaUpload';
import { TextField } from './TextField';
import { Button } from './Button';
import { Icon } from './Icon';
import type { RootState } from '../../store';

/**
 * Props for MessageInput component
 */
export interface MessageInputProps {
  /** Conversation ID */
  conversationId: string;
  /** Whether the input is disabled */
  disabled?: boolean;
  /** Reply information */
  replyTo?: {
    id: string;
    content: string;
    sender: string;
  } | null;
  /** Cancel reply handler */
  onCancelReply?: () => void;
  /** Custom class name */
  className?: string;
}

/**
 * Enhanced MessageInput component for sending messages
 * 
 * Features:
 * - Auto-expanding text input using TextField component
 * - Typing indicators with real-time updates
 * - File upload integration
 * - Reply functionality with preview
 * - Character count and validation
 * - Send button with loading states
 * - Keyboard shortcuts (Enter to send, Shift+Enter for new line)
 * 
 * @example
 * ```tsx
 * <MessageInput
 *   conversationId="conv-123"
 *   disabled={!isConnected}
 *   replyTo={replyMessage}
 *   onCancelReply={() => setReplyMessage(null)}
 * />
 * ```
 */
export const MessageInput: React.FC<MessageInputProps> = ({
  conversationId,
  disabled = false,
  replyTo,
  onCancelReply,
  className = '',
}) => {
  const [message, setMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [uploadingFiles, setUploadingFiles] = useState<string[]>([]);
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});
  const typingTimeoutRef = useRef<number | undefined>(undefined);

  const { sendMessage, startTyping, stopTyping, isConnected } = useSocket();
  const loading = useSelector((state: RootState) => state.messages.loading);

  // Handle input change with typing indicators
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setMessage(value);

    // Handle typing indicators
    if (value && !isTyping && isConnected) {
      setIsTyping(true);
      startTyping(conversationId);
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout to stop typing
    typingTimeoutRef.current = window.setTimeout(() => {
      if (isTyping) {
        setIsTyping(false);
        stopTyping(conversationId);
      }
    }, 2000);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!message.trim() || disabled || loading) return;

    const messageContent = message.trim();
    setMessage('');

    // Stop typing indicator
    if (isTyping) {
      setIsTyping(false);
      stopTyping(conversationId);
    }

    // Clear typing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    try {
      await sendMessage(conversationId, messageContent, 'TEXT');

      // Clear reply if exists
      onCancelReply?.();
    } catch (error) {
      console.error('Failed to send message:', error);
      // Restore message on error
      setMessage(messageContent);
    }
  };

  // Handle keyboard shortcuts
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  // Handle file upload completion
  const handleFileUpload = async (files: any[]) => {
    console.log('File upload completed:', files);
    // File upload logic is handled by SimpleMediaUpload component
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      if (isTyping) {
        stopTyping(conversationId);
      }
    };
  }, [isTyping, conversationId, stopTyping]);

  const containerClasses = [
    'border-t border-border-glass bg-bg-glass p-4',
    className,
  ].filter(Boolean).join(' ');

  return (
    <div className={containerClasses}>
      {/* Reply Preview */}
      {replyTo && (
        <div className="mb-3 p-3 bg-bg-glass-light rounded-lg border border-border-glass">
          <div className="flex items-center justify-between mb-1">
            <span className="text-xs font-medium text-primary">
              Replying to {replyTo.sender}
            </span>
            <button
              onClick={onCancelReply}
              className="text-text-tertiary hover:text-text-secondary transition-colors"
            >
              <Icon name="x" size={14} />
            </button>
          </div>
          <p className="text-sm text-text-secondary truncate">
            {replyTo.content}
          </p>
        </div>
      )}

      {/* Upload Progress */}
      {uploadingFiles.length > 0 && (
        <div className="mb-3 space-y-2">
          {uploadingFiles.map(fileId => (
            <div key={fileId} className="flex items-center space-x-2">
              <Icon name="file" className="text-text-secondary" size={16} />
              <div className="flex-1 bg-bg-glass-light rounded-full h-2">
                <div 
                  className="bg-primary h-2 rounded-full transition-all duration-300"
                  style={{ width: `${uploadProgress[fileId] || 0}%` }}
                />
              </div>
              <span className="text-xs text-text-secondary">
                {uploadProgress[fileId] || 0}%
              </span>
            </div>
          ))}
        </div>
      )}

      {/* Input Form */}
      <form onSubmit={handleSubmit} className="flex items-end space-x-3">
        {/* Media Upload Button */}
        <div className="flex-shrink-0">
          <SimpleMediaUpload
            conversationId={conversationId}
            onUploadComplete={handleFileUpload}
            disabled={disabled}
          />
        </div>

        {/* Message Input */}
        <div className="flex-1">
          <TextField
            value={message}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder={disabled ? 'Connecting...' : 'Type a message...'}
            disabled={disabled || loading}
            autoExpand
            minRows={1}
            maxRows={5}
            maxLength={4000}
            showCharacterCount={message.length > 3800}
            fullWidth
            resizable={false}
            className="border-border-glass focus:border-primary focus:ring-primary/50"
          />
        </div>

        {/* Send Button */}
        <div className="flex-shrink-0">
          <Button
            type="submit"
            variant="primary"
            size="medium"
            disabled={!message.trim() || disabled || loading}
            icon={loading ? "loader" : "send"}
            loading={loading}
          >
            {loading ? 'Sending...' : 'Send'}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default MessageInput;
