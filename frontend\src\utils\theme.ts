// Theme utility functions and constants
export type Theme = 'light' | 'dark';

// Theme color definitions based on NewChatUI reference files
export const themeColors = {
  light: {
    // Background colors
    bg: {
      primary: 'bg-gray-50',
      secondary: 'bg-white',
      tertiary: 'bg-gray-100',
      sidebar: 'bg-white',
      chat: 'bg-gray-50',
      input: 'bg-gray-100',
      hover: 'hover:bg-gray-100',
      message: {
        sent: 'bg-teal-500',
        received: 'bg-white',
        system: 'bg-gray-100'
      }
    },
    // Text colors
    text: {
      primary: 'text-gray-900',
      secondary: 'text-gray-600',
      tertiary: 'text-gray-500',
      placeholder: 'text-gray-400',
      inverse: 'text-white',
      accent: 'text-teal-500',
      message: {
        sent: 'text-white',
        received: 'text-gray-900',
        system: 'text-gray-600'
      }
    },
    // Border colors
    border: {
      primary: 'border-gray-200',
      secondary: 'border-gray-100',
      focus: 'border-teal-500'
    },
    // Ring colors for focus states
    ring: {
      primary: 'ring-teal-500',
      secondary: 'ring-gray-200'
    }
  },
  dark: {
    // Background colors
    bg: {
      primary: 'bg-gray-900',
      secondary: 'bg-gray-800',
      tertiary: 'bg-gray-700',
      sidebar: 'bg-gray-800',
      chat: 'bg-gray-900',
      input: 'bg-gray-700',
      hover: 'hover:bg-gray-700',
      message: {
        sent: 'bg-teal-500',
        received: 'bg-gray-800',
        system: 'bg-gray-700'
      }
    },
    // Text colors
    text: {
      primary: 'text-white',
      secondary: 'text-gray-300',
      tertiary: 'text-gray-400',
      placeholder: 'text-gray-500',
      inverse: 'text-gray-900',
      accent: 'text-teal-400',
      message: {
        sent: 'text-white',
        received: 'text-white',
        system: 'text-gray-300'
      }
    },
    // Border colors
    border: {
      primary: 'border-gray-700',
      secondary: 'border-gray-600',
      focus: 'border-teal-500'
    },
    // Ring colors for focus states
    ring: {
      primary: 'ring-teal-500',
      secondary: 'ring-gray-700'
    }
  }
};

// Utility function to get theme classes
export const getThemeClasses = (theme: Theme) => themeColors[theme];

// Utility function to combine theme classes
export const combineThemeClasses = (theme: Theme, ...classKeys: string[]) => {
  const themeClasses = getThemeClasses(theme);
  return classKeys.map(key => {
    const keys = key.split('.');
    let value: any = themeClasses;
    for (const k of keys) {
      value = value?.[k];
    }
    return value || '';
  }).filter(Boolean).join(' ');
};

// Common component theme patterns
export const getComponentTheme = (theme: Theme) => ({
  // Button themes
  button: {
    primary: `${themeColors[theme].bg.tertiary} ${themeColors[theme].text.primary} ${themeColors[theme].bg.hover}`,
    secondary: `${themeColors[theme].bg.secondary} ${themeColors[theme].text.secondary} ${themeColors[theme].border.primary} border`,
    accent: `bg-teal-500 text-white hover:bg-teal-600`,
    icon: `${themeColors[theme].text.tertiary} ${themeColors[theme].bg.hover} rounded-full`
  },
  
  // Input themes
  input: {
    primary: `${themeColors[theme].bg.input} ${themeColors[theme].text.primary} ${themeColors[theme].text.placeholder} focus:outline-none focus:ring-2 ${themeColors[theme].ring.primary}`,
    search: `${themeColors[theme].bg.input} ${themeColors[theme].text.primary} ${themeColors[theme].text.placeholder} pl-10 pr-4 py-2 rounded-lg text-sm focus:outline-none focus:ring-2 ${themeColors[theme].ring.primary}`
  },
  
  // Card themes
  card: {
    primary: `${themeColors[theme].bg.secondary} ${themeColors[theme].border.primary} border`,
    conversation: `${themeColors[theme].bg.secondary} ${themeColors[theme].bg.hover} cursor-pointer transition-colors`
  },
  
  // Message themes
  message: {
    sent: `${themeColors[theme].bg.message.sent} ${themeColors[theme].text.message.sent} rounded-lg p-3 max-w-xs ml-auto`,
    received: `${themeColors[theme].bg.message.received} ${themeColors[theme].text.message.received} rounded-lg p-3 max-w-xs`,
    system: `${themeColors[theme].bg.message.system} ${themeColors[theme].text.message.system} rounded-lg p-2 text-center text-sm`
  }
});

// Theme transition classes
export const themeTransition = 'transition-colors duration-200 ease-in-out';

// Utility to check if current theme is dark
export const isDarkTheme = (theme: Theme) => theme === 'dark';

// Utility to get opposite theme
export const getOppositeTheme = (theme: Theme): Theme => theme === 'light' ? 'dark' : 'light';
