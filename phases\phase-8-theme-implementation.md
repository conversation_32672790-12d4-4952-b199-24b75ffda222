# Phase 8: Comprehensive Theme Implementation

## Overview
This document outlines the implementation of a comprehensive dark and light theme system for the chat application frontend, building upon existing theme infrastructure while addressing inconsistencies and expanding theme coverage.

## Current Theme Implementation Analysis

### Theme Context System (ThemeContext.tsx)
- **Theme Management**: Centralized theme state with support for light, dark, and system preferences
- **Theme Detection**: Automatic system theme detection using `window.matchMedia('(prefers-color-scheme: dark)')`
- **DOM Integration**: Applies theme via `data-theme` attribute and CSS classes on document root
- **Persistence**: Theme preference stored in localStorage with fallback to system preference
- **Context API**: Provides `theme`, `setTheme`, and `effectiveTheme` through React Context

### Theme Classes Hook (useThemeClasses.ts)
- **Dynamic Class Generation**: Generates theme-aware classes based on effective theme
- **Component Categories**: Structured classes for layout, buttons, inputs, cards, messages, text, backgrounds, and borders
- **Utility Functions**: `getThemeClasses()` and `getComponentTheme()` for flexible class generation
- **Class Combination**: `combine()` utility for merging multiple class paths
- **Dark Mode Detection**: Uses `effectiveTheme === 'dark'` for conditional styling

### Component Theme Usage Analysis

#### Login.tsx & Register.tsx
- **Theme Integration**: Uses `useTheme` hook for theme state and toggle functionality
- **Dynamic Styling**: Applies theme-based classes using template literals with `effectiveTheme`
- **Toggle Controls**: Theme switcher buttons with conditional icon rendering
- **Color Classes**: Direct Tailwind usage with theme conditionals (e.g., `effectiveTheme === 'dark' ? 'text-gray-200' : 'text-gray-800'`)

#### Dashboard.tsx
- **Theme Classes Usage**: Extensively uses `useThemeClasses` for consistent styling
- **Dynamic Class Application**: Applies theme classes conditionally based on `effectiveTheme` and `isDark`
- **Component Coverage**: Layout, sidebar, buttons, search, chat areas all use theme classes
- **Mixed Approach**: Combines `useThemeClasses` with direct Tailwind conditionals

#### Sidebar.tsx
- **Limited Theme Integration**: Does not explicitly use `useTheme` or `useThemeClasses`
- **Inconsistent Styling**: Uses hardcoded Tailwind classes without theme awareness
- **Missing Theme Support**: Tab styling, search inputs, and list items lack theme integration

#### MessageContainer.tsx
- **Partial Theme Usage**: Uses some theme-aware classes (`bg-bg-primary`, `text-text-secondary`)
- **Inconsistent Application**: Mix of theme classes and hardcoded Tailwind classes
- **Missing Theme Hook**: Does not leverage `useThemeClasses` for consistent styling
- **Status Indicators**: Connection status and error states lack theme integration

#### Message Components (TextMessage.tsx, MessageWrapper.tsx, MessageInput.tsx)
- **TextMessage.tsx**:
  - Uses `useThemeClasses` hook to access `isDark` state
  - Implements conditional styling based on dark mode for message bubbles
  - Uses hardcoded color values (teal-500, gray-700, gray-200) instead of theme variables
  - Inconsistent theme class usage with mix of direct color references and theme-aware classes

- **MessageWrapper.tsx**:
  - Uses `useThemeClasses` hook to access `isDark` state
  - Implements conditional styling for timestamps and status indicators
  - Uses hardcoded color values (gray-300, gray-500, gray-700, gray-600) instead of theme variables
  - Inconsistent theme implementation with mix of hardcoded colors and theme-aware classes
  - Uses glass classes without proper theme variable integration

- **MessageInput.tsx**:
  - Does not explicitly use theme hooks (`useTheme` or `useThemeClasses`)
  - Uses theme-aware classes for container styling (`bg-bg-glass`, `border-border-glass`)
  - Relies on component-level theming through TextField and Button components
  - Inconsistent direct usage of theme classes without proper hook integration
  - Reply preview uses theme-aware classes (`bg-bg-glass-light`, `text-primary`, `text-text-tertiary`)

## Required Modifications for Consistent Theme Switching

### 1. Sidebar.tsx Theme Integration
**Current Issues:**
- No theme hook usage
- Hardcoded color classes
- Inconsistent with other components

**Required Changes:**
```typescript
// Add theme imports
import { useTheme } from '../contexts/ThemeContext';
import { useThemeClasses } from '../hooks/useThemeClasses';

// Add theme hooks in component
const { effectiveTheme } = useTheme();
const classes = useThemeClasses();

// Replace hardcoded classes with theme classes
// Before: className="bg-white border-r border-gray-200"
// After: className={classes.layout.sidebar}
```

### 2. MessageContainer.tsx Theme Consistency
**Current Issues:**
- Partial theme class usage
- Mixed styling approaches
- Missing theme integration in status indicators

**Required Changes:**
```typescript
// Add theme hook usage
const { effectiveTheme } = useTheme();
const classes = useThemeClasses();

// Standardize theme class usage
// Replace hardcoded classes with theme-aware classes
// Ensure all UI elements use consistent theme styling
```

### 3. Standardize Theme Class Usage Across Components
**Current Issues:**
- Inconsistent usage patterns between components
- Mix of `useThemeClasses` and direct Tailwind conditionals
- No standardized approach for theme application

**Required Changes:**
- Establish consistent pattern for theme class application
- Create component-level theme utilities if needed
- Standardize theme toggle button implementation

## Theme Variable Definitions

### Color System
```css
/* Dark Theme Variables */
:root[data-theme="dark"] {
  /* Background Colors */
  --color-bg-primary: #0D1117;
  --color-bg-secondary: #161B22;
  --color-bg-tertiary: #21262D;
  --color-bg-glass: rgba(255, 255, 255, 0.05);
  
  /* Text Colors */
  --color-text-primary: #E6EDF3;
  --color-text-secondary: #8B949E;
  --color-text-tertiary: #484F58;
  --color-text-inverse: #0D1117;
  
  /* Interactive Colors */
  --color-primary: #238636;
  --color-primary-hover: #2EA043;
  --color-secondary: #1F6FEB;
  --color-secondary-hover: #388BFD;
  
  /* State Colors */
  --color-success: #238636;
  --color-warning: #F85149;
  --color-error: #DA3633;
  --color-info: #58A6FF;
  
  /* Border Colors */
  --color-border-primary: #30363D;
  --color-border-secondary: #21262D;
  --color-border-glass: rgba(255, 255, 255, 0.1);
}

/* Light Theme Variables */
:root[data-theme="light"] {
  /* Background Colors */
  --color-bg-primary: #FFFFFF;
  --color-bg-secondary: #F6F8FA;
  --color-bg-tertiary: #E1E4E8;
  --color-bg-glass: rgba(0, 0, 0, 0.05);
  
  /* Text Colors */
  --color-text-primary: #1F2328;
  --color-text-secondary: #656D76;
  --color-text-tertiary: #8C959F;
  --color-text-inverse: #FFFFFF;
  
  /* Interactive Colors */
  --color-primary: #1F883D;
  --color-primary-hover: #1A7F37;
  --color-secondary: #0969DA;
  --color-secondary-hover: #0860CA;
  
  /* State Colors */
  --color-success: #1F883D;
  --color-warning: #D1242F;
  --color-error: #CF222E;
  --color-info: #0969DA;
  
  /* Border Colors */
  --color-border-primary: #D1D9E0;
  --color-border-secondary: #E1E4E8;
  --color-border-glass: rgba(0, 0, 0, 0.1);
}
```

### Typography System
```css
/* Font Families */
--font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
--font-family-mono: 'Fira Code', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;

/* Font Sizes */
--font-size-xs: 0.75rem;    /* 12px */
--font-size-sm: 0.875rem;   /* 14px */
--font-size-base: 1rem;     /* 16px */
--font-size-lg: 1.125rem;   /* 18px */
--font-size-xl: 1.25rem;    /* 20px */
--font-size-2xl: 1.5rem;    /* 24px */
--font-size-3xl: 1.875rem;  /* 30px */

/* Font Weights */
--font-weight-normal: 400;
--font-weight-medium: 500;
--font-weight-semibold: 600;
--font-weight-bold: 700;

/* Line Heights */
--line-height-tight: 1.25;
--line-height-normal: 1.5;
--line-height-relaxed: 1.75;
```

### Spacing System
```css
/* Base Spacing Unit: 4px */
--spacing-1: 0.25rem;   /* 4px */
--spacing-2: 0.5rem;    /* 8px */
--spacing-3: 0.75rem;   /* 12px */
--spacing-4: 1rem;      /* 16px */
--spacing-5: 1.25rem;   /* 20px */
--spacing-6: 1.5rem;    /* 24px */
--spacing-8: 2rem;      /* 32px */
--spacing-10: 2.5rem;   /* 40px */
--spacing-12: 3rem;     /* 48px */
--spacing-16: 4rem;     /* 64px */
--spacing-20: 5rem;     /* 80px */
--spacing-24: 6rem;     /* 96px */

/* Component-Specific Spacing */
--spacing-sidebar-width: 320px;
--spacing-header-height: 64px;
--spacing-input-height: 48px;
--spacing-button-height: 40px;
--spacing-avatar-size: 40px;
--spacing-message-max-width: 600px;
```

### Border Radius System
```css
--radius-none: 0;
--radius-sm: 0.125rem;    /* 2px */
--radius-base: 0.25rem;   /* 4px */
--radius-md: 0.375rem;    /* 6px */
--radius-lg: 0.5rem;      /* 8px */
--radius-xl: 0.75rem;     /* 12px */
--radius-2xl: 1rem;       /* 16px */
--radius-full: 9999px;
```

### Shadow System
```css
/* Dark Theme Shadows */
--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
--shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
--shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -2px rgba(0, 0, 0, 0.3);

/* Light Theme Shadows */
--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
--shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
--shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
```

## Implementation Approach for Theme Provider

### Enhanced ThemeContext.tsx
```typescript
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export type Theme = 'light' | 'dark' | 'system';

interface ThemeContextType {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  effectiveTheme: 'light' | 'dark';
  systemTheme: 'light' | 'dark';
  isDark: boolean;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: ReactNode;
  defaultTheme?: Theme;
  storageKey?: string;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ 
  children, 
  defaultTheme = 'system',
  storageKey = 'chatapp-theme'
}) => {
  const [theme, setThemeState] = useState<Theme>(defaultTheme);
  const [systemTheme, setSystemTheme] = useState<'light' | 'dark'>('light');

  // Get system theme preference
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleChange = (e: MediaQueryListEvent) => {
      setSystemTheme(e.matches ? 'dark' : 'light');
    };

    setSystemTheme(mediaQuery.matches ? 'dark' : 'light');
    mediaQuery.addEventListener('change', handleChange);
    
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  // Load theme from localStorage
  useEffect(() => {
       const savedTheme = localStorage.getItem(storageKey) as Theme;
    if (savedTheme && ['light', 'dark', 'system'].includes(savedTheme)) {
      setThemeState(savedTheme);
    }
  }, [storageKey]);

  // Save theme to localStorage and apply to DOM
  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme);
    localStorage.setItem(storageKey, newTheme);
  };

  // Calculate effective theme
  const effectiveTheme = theme === 'system' ? systemTheme : theme;
  const isDark = effectiveTheme === 'dark';

  // Apply theme to document
  useEffect(() => {
    const root = document.documentElement;
    
    // Remove existing theme classes
    root.classList.remove('light', 'dark');
    root.removeAttribute('data-theme');
    
    // Apply new theme
    root.classList.add(effectiveTheme);
    root.setAttribute('data-theme', effectiveTheme);
    
    // Apply CSS custom properties
    if (isDark) {
      root.style.setProperty('--color-bg-primary', '#0D1117');
      root.style.setProperty('--color-text-primary', '#E6EDF3');
      // ... apply all dark theme variables
    } else {
      root.style.setProperty('--color-bg-primary', '#FFFFFF');
      root.style.setProperty('--color-text-primary', '#1F2328');
      // ... apply all light theme variables
    }
  }, [effectiveTheme, isDark]);

  const value: ThemeContextType = {
    theme,
    setTheme,
    effectiveTheme,
    systemTheme,
    isDark
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};
```

### Enhanced useThemeClasses.ts
```typescript
import { useTheme } from '../contexts/ThemeContext';

interface ThemeClasses {
  layout: {
    container: string;
    sidebar: string;
    main: string;
    header: string;
    content: string;
  };
  buttons: {
    primary: string;
    secondary: string;
    outline: string;
    ghost: string;
    danger: string;
  };
  inputs: {
    base: string;
    focus: string;
    error: string;
    disabled: string;
  };
  cards: {
    base: string;
    hover: string;
    selected: string;
  };
  messages: {
    own: string;
    other: string;
    system: string;
  };
  text: {
    primary: string;
    secondary: string;
    tertiary: string;
    inverse: string;
    danger: string;
  };
  background: {
    primary: string;
    secondary: string;
    tertiary: string;
    glass: string;
  };
  border: {
    primary: string;
    secondary: string;
    glass: string;
  };
}

export const useThemeClasses = () => {
  const { effectiveTheme, isDark } = useTheme();

  const classes: ThemeClasses = {
    layout: {
      container: `min-h-screen ${isDark ? 'bg-bg-primary text-text-primary' : 'bg-bg-primary text-text-primary'}`,
      sidebar: `${isDark ? 'bg-bg-secondary border-border-primary' : 'bg-bg-secondary border-border-primary'} w-80 border-r`,
      main: `flex-1 ${isDark ? 'bg-bg-primary' : 'bg-bg-primary'}`,
      header: `${isDark ? 'bg-bg-secondary border-border-primary' : 'bg-bg-secondary border-border-primary'} border-b`,
      content: `flex-1 ${isDark ? 'bg-bg-primary' : 'bg-bg-primary'}`
    },
    buttons: {
      primary: `${isDark ? 'bg-primary hover:bg-primary-hover' : 'bg-primary hover:bg-primary-hover'} text-white transition-colors`,
      secondary: `${isDark ? 'bg-secondary hover:bg-secondary-hover' : 'bg-secondary hover:bg-secondary-hover'} text-white transition-colors`,
      outline: `${isDark ? 'border-border-primary text-text-primary hover:bg-bg-tertiary' : 'border-border-primary text-text-primary hover:bg-bg-tertiary'} border transition-colors`,
      ghost: `${isDark ? 'text-text-secondary hover:bg-bg-tertiary' : 'text-text-secondary hover:bg-bg-tertiary'} transition-colors`,
      danger: `${isDark ? 'bg-error hover:bg-red-600' : 'bg-error hover:bg-red-600'} text-white transition-colors`
    },
    inputs: {
      base: `${isDark ? 'bg-bg-secondary border-border-primary text-text-primary' : 'bg-bg-secondary border-border-primary text-text-primary'} border rounded-lg`,
      focus: `${isDark ? 'border-primary focus:ring-primary' : 'border-primary focus:ring-primary'} focus:ring-2 focus:border-transparent`,
      error: `${isDark ? 'border-error focus:ring-error' : 'border-error focus:ring-error'} focus:ring-2`,
      disabled: `${isDark ? 'bg-bg-tertiary text-text-tertiary cursor-not-allowed' : 'bg-bg-tertiary text-text-tertiary cursor-not-allowed'} opacity-50`
    },
    cards: {
      base: `${isDark ? 'bg-bg-secondary border-border-primary' : 'bg-bg-secondary border-border-primary'} border rounded-lg`,
      hover: `${isDark ? 'hover:bg-bg-tertiary' : 'hover:bg-bg-tertiary'} transition-colors cursor-pointer`,
      selected: `${isDark ? 'bg-primary/10 border-primary' : 'bg-primary/10 border-primary'} border-2`
    },
    messages: {
      own: `${isDark ? 'bg-primary text-white' : 'bg-primary text-white'} rounded-lg`,
      other: `${isDark ? 'bg-bg-secondary text-text-primary' : 'bg-bg-secondary text-text-primary'} rounded-lg`,
      system: `${isDark ? 'bg-bg-tertiary text-text-secondary' : 'bg-bg-tertiary text-text-secondary'} rounded-lg text-center`
    },
    text: {
      primary: isDark ? 'text-text-primary' : 'text-text-primary',
      secondary: isDark ? 'text-text-secondary' : 'text-text-secondary',
      tertiary: isDark ? 'text-text-tertiary' : 'text-text-tertiary',
      inverse: isDark ? 'text-text-inverse' : 'text-text-inverse',
      danger: 'text-error'
    },
    background: {
      primary: isDark ? 'bg-bg-primary' : 'bg-bg-primary',
      secondary: isDark ? 'bg-bg-secondary' : 'bg-bg-secondary',
      tertiary: isDark ? 'bg-bg-tertiary' : 'bg-bg-tertiary',
      glass: isDark ? 'bg-bg-glass backdrop-blur' : 'bg-bg-glass backdrop-blur'
    },
    border: {
      primary: isDark ? 'border-border-primary' : 'border-border-primary',
      secondary: isDark ? 'border-border-secondary' : 'border-border-secondary',
      glass: isDark ? 'border-border-glass' : 'border-border-glass'
    }
  };

  return {
    classes,
    isDark,
    effectiveTheme,
    getThemeClasses: (baseClasses: string) => {
      return baseClasses.replace(/\b(?:dark|light):/g, `${effectiveTheme}:`);
    },
    getComponentTheme: (component: keyof ThemeClasses) => {
      return classes[component];
    },
    combine: (...classPaths: string[]) => {
      return classPaths.map(path => {
        const keys = path.split('.');
        let current: any = classes;
        for (const key of keys) {
          current = current?.[key];
        }
        return current || '';
      }).filter(Boolean).join(' ');
    }
  };
};
```

## Component-Level Theme Adaptation Guidelines

### Standard Component Structure
```typescript
import React from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { useThemeClasses } from '../hooks/useThemeClasses';

interface ComponentProps {
  // ... other props
  className?: string;
}

const Component: React.FC<ComponentProps> = ({ className, ...props }) => {
  const { effectiveTheme, isDark } = useTheme();
  const { classes, combine } = useThemeClasses();

  return (
    <div className={combine('layout.container', className)}>
      {/* Component content */}
    </div>
  );
};
```

### Theme Toggle Button Standardization
```typescript
import React from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { Icon } from './ui/Icon';

const ThemeToggle: React.FC = () => {
  const { theme, setTheme, effectiveTheme } = useTheme();

  const toggleTheme = () => {
    const themes: Theme[] = ['light', 'dark', 'system'];
    const currentIndex = themes.indexOf(theme);
    const nextIndex = (currentIndex + 1) % themes.length;
    setTheme(themes[nextIndex]);
  };

  return (
    <button
      onClick={toggleTheme}
      className="p-2 rounded-lg transition-colors hover:bg-bg-tertiary"
      title={`Current: ${effectiveTheme} (${theme === 'system' ? 'system' : 'manual'})`}
    >
      <Icon 
        name={effectiveTheme === 'dark' ? 'moon' : 'sun'} 
        className="w-5 h-5 text-text-secondary"
      />
    </button>
  );
};
```

### Form Component Theme Guidelines
```typescript
// Input Components
const ThemedInput: React.FC<InputProps> = ({ error, disabled, className, ...props }) => {
  const { classes, combine } = useThemeClasses();
  
  return (
    <input
      className={combine(
        'inputs.base',
        error ? 'inputs.error' : 'inputs.focus',
        disabled ? 'inputs.disabled' : '',
        className
      )}
      disabled={disabled}
      {...props}
    />
  );
};

// Button Components
const ThemedButton: React.FC<ButtonProps> = ({ variant = 'primary', disabled, className, ...props }) => {
  const { classes, combine } = useThemeClasses();
  
  const buttonClass = combine(
    'buttons.' + variant,
    disabled ? 'opacity-50 cursor-not-allowed' : '',
    className
  );
  
  return (
    <button
      className={buttonClass}
      disabled={disabled}
      {...props}
    />
  );
};
```

### Card Component Theme Guidelines
```typescript
const ThemedCard: React.FC<CardProps> = ({ selected, hoverable, className, ...props }) => {
  const { classes, combine } = useThemeClasses();
  
  return (
    <div
      className={combine(
        'cards.base',
        selected ? 'cards.selected' : '',
        hoverable ? 'cards.hover' : '',
        className
      )}
      {...props}
    />
  );
};
```

### Message Component Theme Guidelines
```typescript
const ThemedMessage: React.FC<MessageProps> = ({ isOwn, isSystem, className, ...props }) => {
  const { classes, combine } = useThemeClasses();
  
  const messageClass = combine(
    isSystem ? 'messages.system' : isOwn ? 'messages.own' : 'messages.other',
    className
  );
  
  return (
    <div
      className={messageClass}
      {...props}
    />
  );
};
```

## Testing Strategy for Theme Consistency

### Visual Regression Testing
```typescript
// theme-visual.test.ts
import { test, expect } from '@playwright/test';

test.describe('Theme Visual Consistency', () => {
  test('dark theme renders correctly', async ({ page }) => {
    await page.goto('/login');
    await page.evaluate(() => {
      localStorage.setItem('chatapp-theme', 'dark');
      window.location.reload();
    });
    
    await page.waitForTimeout(1000);
    
    // Take screenshot for visual comparison
    await expect(page).toHaveScreenshot('login-dark.png');
    
    // Test specific elements
    const loginButton = page.locator('button[type="submit"]');
    await expect(loginButton).toHaveCSS('background-color', 'rgb(35, 134, 54)');
  });

  test('light theme renders correctly', async ({ page }) => {
    await page.goto('/login');
    await page.evaluate(() => {
      localStorage.setItem('chatapp-theme', 'light');
      window.location.reload();
    });
    
    await page.waitForTimeout(1000);
    
    // Take screenshot for visual comparison
    await expect(page).toHaveScreenshot('login-light.png');
    
    // Test specific elements
    const loginButton = page.locator('button[type="submit"]');
    await expect(loginButton).toHaveCSS('background-color', 'rgb(31, 136, 61)');
  });

  test('theme switching works correctly', async ({ page }) => {
    await page.goto('/login');
    
    // Start with light theme
    await page.evaluate(() => {
      localStorage.setItem('chatapp-theme', 'light');
      window.location.reload();
    });
    
    // Switch to dark theme
    await page.click('[data-testid="theme-toggle"]');
    
    // Verify theme changed
    const html = page.locator('html');
    await expect(html).toHaveAttribute('data-theme', 'dark');
    
    // Verify localStorage updated
    const theme = await page.evaluate(() => localStorage.getItem('chatapp-theme'));
    expect(theme).toBe('dark');
  });
});
```

### Accessibility Testing
```typescript
// theme-accessibility.test.ts
import { test, expect } from '@playwright/test';

test.describe('Theme Accessibility', () => {
  test('color contrast meets WCAG standards', async ({ page }) => {
    await page.goto('/login');
    
    // Test dark theme contrast
    await page.evaluate(() => {
      localStorage.setItem('chatapp-theme', 'dark');
      window.location.reload();
    });
    
    const loginButton = page.locator('button[type="submit"]');
    const buttonColor = await loginButton.evaluate(el => 
      window.getComputedStyle(el).backgroundColor
    );
    const textColor = await loginButton.evaluate(el => 
      window.getComputedStyle(el).color
    );
    
    // Verify contrast ratio (simplified check)
    expect(buttonColor).not.toBe(textColor);
  });

  test('focus indicators are visible in both themes', async ({ page }) => {
    await page.goto('/login');
    
    const emailInput = page.locator('input[type="email"]');
    await emailInput.focus();
    
    // Check if focus ring is visible
    const focusRing = await emailInput.evaluate(el => {
      const style = window.getComputedStyle(el);
      return style.outline || style.boxShadow;
    });
    
    expect(focusRing).toBeTruthy();
  });

  test('theme toggle button has proper ARIA labels', async ({ page }) => {
    await page.goto('/login');
    
    const themeToggle = page.locator('[data-testid="theme-toggle"]');
    await expect(themeToggle).toHaveAttribute('title');
    await expect(themeToggle).toHaveAttribute('aria-label');
  });
});
```

### Component-Level Theme Testing
```typescript
// ComponentTheme.test.tsx
import React from 'react';
import { render, screen } from '@testing-library/react';
import { ThemeProvider } from '../contexts/ThemeContext';
import { Sidebar } from '../components/ui/Sidebar';

describe('Sidebar Theme Integration', () => {
  test('applies correct theme classes in dark mode', () => {
    render(
      <ThemeProvider defaultTheme="dark">
        <Sidebar />
      </ThemeProvider>
    );
    
    const sidebar = screen.getByRole('complementary');
    expect(sidebar).toHaveClass('bg-bg-secondary');
    expect(sidebar).toHaveClass('border-border-primary');
  });

  test('applies correct theme classes in light mode', () => {
    render(
      <ThemeProvider defaultTheme="light">
        <Sidebar />
      </ThemeProvider>
    );
    
    const sidebar = screen.getByRole('complementary');
    expect(sidebar).toHaveClass('bg-bg-secondary');
    expect(sidebar).toHaveClass('border-border-primary');
  });
});
```

### Manual Testing Checklist
- [ ] Theme switching works smoothly without page reload
- [ ] All text remains readable in both themes
- [ ] Interactive elements maintain proper contrast
- [ ] Glass morphism effects work correctly in both themes
- [ ] Message bubbles are distinguishable in both themes
- [ ] Loading states and skeletons adapt to theme
- [ ] Error messages and warnings are visible in both themes
- [ ] Form inputs have proper focus indicators
- [ ] Theme preference persists across sessions
- [ ] System theme changes are detected and applied

## Performance Considerations for Theme Switching

### CSS Optimization
```css
/* Use CSS custom properties for instant theme switching */
:root {
  /* Define all variables upfront */
  --transition-duration: 200ms;
  --transition-timing: ease-in-out;
}

/* Apply transitions only to necessary properties */
* {
  transition: background-color var(--transition-duration) var(--transition-timing),
              color var(--transition-duration) var(--transition-timing),
              border-color var(--transition-duration) var(--transition-timing);
}

/* Avoid transitions on layout properties */
.layout-container,
.sidebar,
.main-content {
  transition: none;
}
```

### JavaScript Optimization
```typescript
// Debounce theme changes to prevent excessive re-renders
const useDebouncedTheme = () => {
  const { theme, setTheme } = useTheme();
  const [debouncedTheme, setDebouncedTheme] = useState(theme);
  
  useEffect(() => {
    const timeout = setTimeout(() => {
      setDebouncedTheme(theme);
    }, 100);
    
    return () => clearTimeout(timeout);
  }, [theme]);
  
  return { theme: debouncedTheme, setTheme };
};

// Memoize theme classes to prevent recalculation
const useMemoizedThemeClasses = () => {
  const { isDark } = useTheme();
  
  return useMemo(() => {
    return {
      primary: isDark ? 'bg-primary hover:bg-primary-hover' : 'bg-primary hover:bg-primary-hover',
      secondary: isDark ? 'bg-bg-secondary' : 'bg-bg-secondary',
      // ... other classes
    };
  }, [isDark]);
};
```

### Component-Level Optimizations
```typescript
// Use React.memo for theme-heavy components
const ThemeAwareComponent = React.memo(({ children, className }) => {
  const { classes } = useThemeClasses();
  
  return (
    <div className={`${classes.background.primary} ${className}`}>
      {children}
    </div>
  );
});

// Lazy load theme-specific assets
const ThemeAssets = lazy(() => import('./ThemeAssets'));

const ThemeProvider: React.FC = ({ children }) => {
  const { effectiveTheme } = useTheme();
  
  return (
    <>
      {children}
      <Suspense fallback={null}>
        <ThemeAssets theme={effectiveTheme} />
      </Suspense>
    </>
  );
};
```

### DOM Manipulation Optimization
```typescript
// Batch DOM updates for theme switching
const useBatchThemeUpdate = () => {
  const { effectiveTheme } = useTheme();
  
  useEffect(() => {
    // Use requestAnimationFrame for smooth theme transitions
    requestAnimationFrame(() => {
      document.documentElement.classList.remove('light', 'dark');
      document.documentElement.classList.add(effectiveTheme);
      document.documentElement.setAttribute('data-theme', effectiveTheme);
    });
  }, [effectiveTheme]);
};
```

### Memory Management
```typescript
// Clean up theme-related event listeners
const useSystemThemeDetection = () => {
  const { setTheme } = useTheme();
  
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleChange = (e: MediaQueryListEvent) => {
      // Only update if user hasn't set a manual preference
      if (localStorage.getItem('chatapp-theme') === 'system') {
        setTheme('system');
      }
    };
    
    mediaQuery.addEventListener('change', handleChange);
    
    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  }, [setTheme]);
};
```

### Performance Monitoring
```typescript
// Monitor theme switching performance
const useThemePerformance = () => {
  const { effectiveTheme } = useTheme();
  
  useEffect(() => {
    const start = performance.now();
    
    // Measure theme application time
    requestAnimationFrame(() => {
      const end = performance.now();
      const duration = end - start;
      
      if (duration > 16) { // More than one frame (60fps)
        console.warn(`Theme switch took ${duration}ms, may cause visual jank`);
      }
    });
  }, [effectiveTheme]);
};
```

## Implementation Timeline

### Phase 1: Foundation (Week 1)
- [ ] Update ThemeContext.tsx with enhanced theme management
- [ ] Enhance useThemeClasses.ts with comprehensive class definitions
- [ ] Create theme variable CSS definitions
- [ ] Implement theme persistence and system detection

### Phase 2: Component Updates (Week 2)
- [ ] Update Sidebar.tsx with full theme integration
- [ ] Update MessageContainer.tsx with consistent theme classes
- [ ] Standardize theme toggle buttons across components
- [ ] Create theme-aware form components

### Phase 3: Testing & Optimization (Week 3)
- [ ] Implement visual regression tests
- [ ] Add accessibility testing for both themes
- [ ] Optimize theme switching performance
- [ ] Add performance monitoring

### Phase 4: Documentation & Deployment (Week 4)
- [ ] Create comprehensive theme documentation
- [ ] Add theme customization guides
- [ ] Deploy and monitor theme performance
- [ ] Gather user feedback and iterate

## Success Metrics

### Performance Metrics
- Theme switch time < 100ms
- No layout shifts during theme changes
- Smooth transitions (60fps)
- Minimal memory usage increase

### User Experience Metrics
- Theme preference persistence rate > 95%
- User-reported theme issues < 1%
- Theme switching usability score > 4.5/5
- Accessibility compliance (WCAG 2.1 AA)

### Code Quality Metrics
- 100% theme coverage across components
- Consistent theme class usage
- Zero theme-related console errors
- Complete TypeScript type safety

This comprehensive theme implementation plan ensures a robust, performant, and user-friendly theming system that enhances the overall chat application experience while maintaining consistency and accessibility standards.