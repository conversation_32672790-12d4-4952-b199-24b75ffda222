[{"name": "App.tsx", "type": "file", "path": "App.tsx", "calls": ["components/ui/ErrorBoundary.tsx", "contexts/AuthContext.tsx", "contexts/EncryptionContext.tsx", "contexts/SocketContext.tsx", "contexts/ThemeContext.tsx", "pages/Dashboard.tsx", "pages/Login.tsx", "pages/NewChatUI.tsx", "pages/Register.tsx"], "children": []}, {"name": "components", "type": "folder", "path": "components", "calls": [], "children": [{"name": "Cha<PERSON>", "type": "folder", "path": "components/Chat", "calls": [], "children": [{"name": "ChatHeader.tsx", "type": "file", "path": "components/Chat/ChatHeader.tsx", "calls": ["components/ui/ProfilePhotoAvatar.tsx", "contexts/SocketContext.tsx"], "children": []}, {"name": "SimpleMediaUpload.tsx", "type": "file", "path": "components/Chat/SimpleMediaUpload.tsx", "calls": ["contexts/SocketContext.tsx"], "children": []}]}, {"name": "Icons", "type": "folder", "path": "components/Icons", "calls": [], "children": [{"name": "Google.tsx", "type": "file", "path": "components/Icons/Google.tsx", "calls": [], "children": []}]}, {"name": "calling", "type": "folder", "path": "components/calling", "calls": [], "children": [{"name": "ActiveCallInterface.tsx", "type": "file", "path": "components/calling/ActiveCallInterface.tsx", "calls": ["components/calling/CallControls.tsx", "components/calling/CallStatusIndicator.tsx", "components/calling/VideoContainer.tsx"], "children": []}, {"name": "CallControls.tsx", "type": "file", "path": "components/calling/CallControls.tsx", "calls": ["contexts/SocketContext.tsx"], "children": []}, {"name": "CallManager.tsx", "type": "file", "path": "components/calling/CallManager.tsx", "calls": ["components/calling/ActiveCallInterface.tsx", "components/calling/IncomingCallModal.tsx", "contexts/SocketContext.tsx"], "children": []}, {"name": "CallStatusIndicator.tsx", "type": "file", "path": "components/calling/CallStatusIndicator.tsx", "calls": [], "children": []}, {"name": "IncomingCallModal.tsx", "type": "file", "path": "components/calling/IncomingCallModal.tsx", "calls": [], "children": []}, {"name": "VideoContainer.tsx", "type": "file", "path": "components/calling/VideoContainer.tsx", "calls": [], "children": []}]}, {"name": "ui", "type": "folder", "path": "components/ui", "calls": [], "children": [{"name": "ApiErrorDisplay.tsx", "type": "file", "path": "components/ui/ApiErrorDisplay.tsx", "calls": ["components/ui/Button.tsx", "components/ui/Icon.tsx"], "children": []}, {"name": "Button.tsx", "type": "file", "path": "components/ui/Button.tsx", "calls": ["components/ui/Icon.tsx"], "children": []}, {"name": "ConversationItem.tsx", "type": "file", "path": "components/ui/ConversationItem.tsx", "calls": ["components/ui/Icon.tsx", "components/ui/ProfilePhotoAvatar.tsx"], "children": []}, {"name": "Divider.tsx", "type": "file", "path": "components/ui/Divider.tsx", "calls": [], "children": []}, {"name": "ErrorBoundary.tsx", "type": "file", "path": "components/ui/ErrorBoundary.tsx", "calls": ["components/ui/Button.tsx", "components/ui/Icon.tsx"], "children": []}, {"name": "Form.tsx", "type": "file", "path": "components/ui/Form.tsx", "calls": ["components/ui/Button.tsx", "components/ui/Icon.tsx"], "children": []}, {"name": "Icon.tsx", "type": "file", "path": "components/ui/Icon.tsx", "calls": ["components/Icons/Google.tsx"], "children": []}, {"name": "Input.tsx", "type": "file", "path": "components/ui/Input.tsx", "calls": ["components/ui/Icon.tsx"], "children": []}, {"name": "LoadingSpinner.tsx", "type": "file", "path": "components/ui/LoadingSpinner.tsx", "calls": ["components/ui/Icon.tsx"], "children": []}, {"name": "MessageInput.tsx", "type": "file", "path": "components/ui/MessageInput.tsx", "calls": ["components/Chat/SimpleMediaUpload.tsx", "components/ui/Button.tsx", "components/ui/Icon.tsx", "components/ui/TextField.tsx", "contexts/SocketContext.tsx"], "children": []}, {"name": "ProfilePhotoAvatar.tsx", "type": "file", "path": "components/ui/ProfilePhotoAvatar.tsx", "calls": ["components/ui/Icon.tsx"], "children": []}, {"name": "SearchBar.tsx", "type": "file", "path": "components/ui/SearchBar.tsx", "calls": ["components/ui/Icon.tsx"], "children": []}, {"name": "Select.tsx", "type": "file", "path": "components/ui/Select.tsx", "calls": ["components/ui/Icon.tsx"], "children": []}, {"name": "Sidebar.tsx", "type": "file", "path": "components/ui/Sidebar.tsx", "calls": ["components/ui/Button.tsx", "components/ui/ConversationItem.tsx", "components/ui/Icon.tsx", "components/ui/SearchBar.tsx", "components/ui/ThemeToggle.tsx", "contexts/AuthContext.tsx"], "children": []}, {"name": "TextField.tsx", "type": "file", "path": "components/ui/TextField.tsx", "calls": ["components/ui/Icon.tsx"], "children": []}, {"name": "ThemeToggle.tsx", "type": "file", "path": "components/ui/ThemeToggle.tsx", "calls": ["components/ui/Icon.tsx", "contexts/ThemeContext.tsx"], "children": []}, {"name": "messages", "type": "folder", "path": "components/ui/messages", "calls": [], "children": [{"name": "AudioMessage.tsx", "type": "file", "path": "components/ui/messages/AudioMessage.tsx", "calls": ["components/ui/Icon.tsx"], "children": []}, {"name": "CallingMessage.tsx", "type": "file", "path": "components/ui/messages/CallingMessage.tsx", "calls": ["components/ui/Icon.tsx"], "children": []}, {"name": "FileMessage.tsx", "type": "file", "path": "components/ui/messages/FileMessage.tsx", "calls": ["components/ui/Icon.tsx"], "children": []}, {"name": "GroupUpdateMessage.tsx", "type": "file", "path": "components/ui/messages/GroupUpdateMessage.tsx", "calls": ["components/ui/Icon.tsx", "components/ui/ProfilePhotoAvatar.tsx"], "children": []}, {"name": "ImageMessage.tsx", "type": "file", "path": "components/ui/messages/ImageMessage.tsx", "calls": ["components/ui/Icon.tsx"], "children": []}, {"name": "MessageWrapper.tsx", "type": "file", "path": "components/ui/messages/MessageWrapper.tsx", "calls": ["components/ui/Icon.tsx", "components/ui/ProfilePhotoAvatar.tsx"], "children": []}, {"name": "TextMessage.tsx", "type": "file", "path": "components/ui/messages/TextMessage.tsx", "calls": ["components/ui/Button.tsx"], "children": []}]}]}]}, {"name": "containers", "type": "folder", "path": "containers", "calls": [], "children": [{"name": "MessageContainer.tsx", "type": "file", "path": "containers/MessageContainer.tsx", "calls": ["contexts/AuthContext.tsx", "contexts/SocketContext.tsx"], "children": []}]}, {"name": "contexts", "type": "folder", "path": "contexts", "calls": [], "children": [{"name": "AuthContext.tsx", "type": "file", "path": "contexts/AuthContext.tsx", "calls": [], "children": []}, {"name": "EncryptionContext.tsx", "type": "file", "path": "contexts/EncryptionContext.tsx", "calls": [], "children": []}, {"name": "SocketContext.tsx", "type": "file", "path": "contexts/SocketContext.tsx", "calls": ["contexts/AuthContext.tsx", "contexts/EncryptionContext.tsx"], "children": []}, {"name": "ThemeContext.tsx", "type": "file", "path": "contexts/ThemeContext.tsx", "calls": [], "children": []}]}, {"name": "pages", "type": "folder", "path": "pages", "calls": [], "children": [{"name": "Dashboard.tsx", "type": "file", "path": "pages/Dashboard.tsx", "calls": ["components/Chat/ChatHeader.tsx", "components/ui/SearchBar.tsx", "containers/MessageContainer.tsx", "contexts/AuthContext.tsx", "contexts/SocketContext.tsx", "contexts/ThemeContext.tsx"], "children": []}, {"name": "Login.tsx", "type": "file", "path": "pages/Login.tsx", "calls": ["components/ui/Divider.tsx", "contexts/ThemeContext.tsx"], "children": []}, {"name": "NewChatUI.tsx", "type": "file", "path": "pages/NewChatUI.tsx", "calls": [], "children": []}, {"name": "Register.tsx", "type": "file", "path": "pages/Register.tsx", "calls": ["contexts/ThemeContext.tsx"], "children": []}]}]