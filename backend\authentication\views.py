# backend/authentication/views.py
from rest_framework.decorators import api_view, permission_classes, throttle_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework.throttling import UserRateThrottle
from django.contrib.auth import authenticate
from users.models import User
from users.schemas import UserCreate, UserLogin, UserResponse, OTPRequest, OTPVerify, OTPResponse
from users.services import OTPService, UserService
from pydantic import ValidationError
import json
import logging

logger = logging.getLogger(__name__)


class OTPRequestThrottle(UserRateThrottle):
    scope = 'otp_request'


@api_view(['POST'])
@permission_classes([AllowAny])
def signup_request_otp(request):
    """Request OTP for signup - check if user exists first"""
    try:
        # Parse and validate input data
        data = json.loads(request.body)
        otp_data = OTPRequest(**data)
        
        # Check if user already exists
        if UserService.user_exists(otp_data.email):
            return Response(
                {'error': 'User already exists - please login instead'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Request OTP for signup
        success, message = OTPService.request_otp(otp_data.email, 'signup')
        
        if success:
            return Response({
                'message': message,
                'email': otp_data.email
            }, status=status.HTTP_200_OK)
        else:
            return Response(
                {'error': message},
                status=status.HTTP_400_BAD_REQUEST
            )
        
    except ValidationError as e:
        return Response(
            {'error': 'Invalid input data', 'details': e.errors()},
            status=status.HTTP_400_BAD_REQUEST
        )
    except json.JSONDecodeError:
        return Response(
            {'error': 'Invalid JSON format'},
            status=status.HTTP_400_BAD_REQUEST
        )
    except Exception as e:
        logger.error(f"Signup OTP request failed: {str(e)}")
        return Response(
            {'error': 'Failed to process signup request'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([AllowAny])
def signup_verify_otp(request):
    """Verify OTP and create new user"""
    try:
        # Parse and validate input data
        data = json.loads(request.body)
        verify_data = OTPVerify(**data)
        
        # Check if user already exists (double check)
        if UserService.user_exists(verify_data.email):
            return Response(
                {'error': 'User already exists - please login instead'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Verify OTP
        otp_valid, otp_error = OTPService.verify_otp(
            verify_data.email, 
            verify_data.otp_code, 
            'signup'
        )
        
        if not otp_valid:
            return Response(
                {'error': otp_error},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Validate required fields for signup
        if not verify_data.name:
            return Response(
                {'error': 'Name is required for signup'},
                status=status.HTTP_400_BAD_REQUEST
            )

        if not verify_data.password:
            return Response(
                {'error': 'Password is required for signup'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Create user with password
        user, user_error = UserService.create_user(verify_data.email, verify_data.name, verify_data.password)

        if not user:
            return Response(
                {'error': user_error},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        
        # Generate JWT tokens
        refresh = RefreshToken.for_user(user)
        access_token = refresh.access_token
        
        # Prepare response data
        user_response = UserResponse(
            id=user.id,
            email=user.email,
            name=user.name,
            profile_picture=user.profile_picture.url if user.profile_picture else None,
            is_verified=user.is_verified,
            is_online=user.is_online,
            last_seen=user.last_seen.isoformat() if user.last_seen else None,
            created_at=user.date_joined.isoformat()
        )
        
        return Response({
            'user': user_response.dict(),
            'access': str(access_token),
            'refresh': str(refresh)
        }, status=status.HTTP_201_CREATED)
        
    except ValidationError as e:
        return Response(
            {'error': 'Invalid input data', 'details': e.errors()},
            status=status.HTTP_400_BAD_REQUEST
        )
    except json.JSONDecodeError:
        return Response(
            {'error': 'Invalid JSON format'},
            status=status.HTTP_400_BAD_REQUEST
        )
    except Exception as e:
        logger.error(f"Signup verification failed: {str(e)}")
        return Response(
            {'error': 'Signup verification failed'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([AllowAny])
# @throttle_classes([OTPRequestThrottle])  # Temporarily disabled for testing
def login_request_otp(request):
    """Request OTP for login - check if user exists first"""
    try:
        # Parse and validate input data
        data = json.loads(request.body)
        otp_data = OTPRequest(**data)
        
        # Check if user exists
        if not UserService.user_exists(otp_data.email):
            return Response(
                {'error': 'No user found'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Request OTP for login
        success, message = OTPService.request_otp(otp_data.email, 'login')
        
        if success:
            return Response({
                'message': message,
                'email': otp_data.email
            }, status=status.HTTP_200_OK)
        else:
            return Response(
                {'error': message},
                status=status.HTTP_400_BAD_REQUEST
            )
        
    except ValidationError as e:
        return Response(
            {'error': 'Invalid input data', 'details': e.errors()},
            status=status.HTTP_400_BAD_REQUEST
        )
    except json.JSONDecodeError:
        return Response(
            {'error': 'Invalid JSON format'},
            status=status.HTTP_400_BAD_REQUEST
        )
    except Exception as e:
        logger.error(f"Login OTP request failed: {str(e)}")
        return Response(
            {'error': 'Failed to process login request'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([AllowAny])
def login_verify_otp(request):
    """Verify OTP and login user"""
    try:
        # Parse and validate input data
        data = json.loads(request.body)
        verify_data = OTPVerify(**data)
        
        # Get user
        user = UserService.get_user_by_email(verify_data.email)
        if not user:
            return Response(
                {'error': 'No user found'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Verify OTP
        otp_valid, otp_error = OTPService.verify_otp(
            verify_data.email, 
            verify_data.otp_code, 
            'login'
        )
        
        if not otp_valid:
            return Response(
                {'error': otp_error},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Generate JWT tokens
        refresh = RefreshToken.for_user(user)
        access_token = refresh.access_token
        
        # Update user online status
        user.is_online = True
        user.save()
        
        # Prepare response data
        user_response = UserResponse(
            id=user.id,
            email=user.email,
            name=user.name,
            profile_picture=user.profile_picture.url if user.profile_picture else None,
            is_verified=user.is_verified,
            is_online=user.is_online,
            last_seen=user.last_seen.isoformat() if user.last_seen else None,
            created_at=user.date_joined.isoformat()
        )
        
        return Response({
            'user': user_response.dict(),
            'access_token': str(access_token),
            'refresh_token': str(refresh)
        }, status=status.HTTP_200_OK)
        
    except ValidationError as e:
        return Response(
            {'error': 'Invalid input data', 'details': e.errors()},
            status=status.HTTP_400_BAD_REQUEST
        )
    except json.JSONDecodeError:
        return Response(
            {'error': 'Invalid JSON format'},
            status=status.HTTP_400_BAD_REQUEST
        )
    except Exception as e:
        logger.error(f"Login verification failed: {str(e)}")
        return Response(
            {'error': 'Login verification failed'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


# Legacy endpoints for backward compatibility (can be removed later)
@api_view(['POST'])
@permission_classes([AllowAny])
def register(request):
    """Legacy register endpoint - redirects to new OTP flow"""
    return Response(
        {'error': 'This endpoint is deprecated. Please use /auth/signup/request-otp/ instead.'},
        status=status.HTTP_410_GONE
    )


@api_view(['POST'])
@permission_classes([AllowAny])
def login(request):
    """Login with email and password"""
    try:
        # Parse and validate request data
        login_data = UserLogin(**request.data)
        
        # Authenticate user
        user = authenticate(
            request=request,
            email=login_data.email,
            password=login_data.password
        )
        
        if not user:
            return Response(
                {'error': 'Invalid email or password'},
                status=status.HTTP_401_UNAUTHORIZED
            )
        
        if not user.is_active:
            return Response(
                {'error': 'Account is deactivated'},
                status=status.HTTP_401_UNAUTHORIZED
            )
        
        # Generate JWT tokens
        refresh = RefreshToken.for_user(user)
        access_token = refresh.access_token
        
        # Update user online status
        user.is_online = True
        user.save(update_fields=['is_online'])
        
        # Prepare user response
        user_response = UserResponse(
            id=user.id,
            email=user.email,
            name=user.name,
            profile_picture=user.profile_picture.url if user.profile_picture else None,
            is_verified=user.is_verified,
            is_online=user.is_online,
            last_seen=user.last_seen.isoformat() if user.last_seen else None,
            created_at=user.date_joined.isoformat()
        )
        
        return Response({
            'access': str(access_token),
            'refresh': str(refresh),
            'user': user_response.dict()
        }, status=status.HTTP_200_OK)
        
    except ValidationError as e:
        return Response(
            {'error': 'Invalid input data', 'details': e.errors()},
            status=status.HTTP_400_BAD_REQUEST
        )
    except json.JSONDecodeError:
        return Response(
            {'error': 'Invalid JSON format'},
            status=status.HTTP_400_BAD_REQUEST
        )
    except Exception as e:
        logger.error(f"Login failed: {str(e)}")
        return Response(
            {'error': 'Login failed'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def profile(request):
    try:
        # Return current user profile
        user_response = UserResponse(
            id=request.user.id,
            email=request.user.email,
            name=request.user.name,
            profile_picture=request.user.profile_picture.url if request.user.profile_picture else None,
            is_verified=request.user.is_verified,
            is_online=request.user.is_online,
            last_seen=request.user.last_seen.isoformat() if request.user.last_seen else None,
            created_at=request.user.date_joined.isoformat()
        )
        return Response({
            'user': user_response.dict()
        }, status=status.HTTP_200_OK)
    except Exception as e:
        logger.error(f"Profile retrieval failed: {str(e)}")
        return Response(
            {'error': 'Failed to retrieve profile'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
