// frontend/src/components/ui/messages/index.ts

// Export all message components
export { TextMessage, type TextMessageProps } from './TextMessage';
export { ImageMessage, type ImageMessageProps } from './ImageMessage';
export { AudioMessage, type AudioMessageProps } from './AudioMessage';
export { FileMessage, type FileMessageProps, type FileInfo } from './FileMessage';

// Export message wrapper and container
export { MessageWrapper, type MessageWrapperProps } from './MessageWrapper';
// TODO: Create MessageContainer component
// export { MessageContainer, type MessageContainerProps } from './MessageContainer';

// Export system message components
export { GroupUpdateMessage, type GroupUpdateMessageProps } from './GroupUpdateMessage';
export { CallingMessage, type CallingMessageProps } from './CallingMessage';
