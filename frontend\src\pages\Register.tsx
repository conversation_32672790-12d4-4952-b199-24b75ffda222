// frontend/src/pages/Register.tsx
import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Form, Input, Button, Icon } from '../components/ui';
import { useTheme } from '../contexts/ThemeContext';
import { useRequestSignupOTPMutation, useVerifySignupOTPMutation } from '../services/authApi';
import * as Yup from 'yup';

interface RegisterForm {
  email: string;
  name: string;
  password: string;
}

interface OTPVerifyForm {
  otpCode: string;
}

// Validation schema for email, name, and password
const registrationSchema = Yup.object().shape({
  email: Yup.string()
    .email('Please enter a valid email address')
    .required('Email is required'),
  name: Yup.string()
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name must be less than 100 characters')
    .required('Name is required'),
  password: Yup.string()
    .min(8, 'Password must be at least 8 characters')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      'Password must contain at least one uppercase letter, one lowercase letter, and one number'
    )
    .required('Password is required'),
});

// Validation schema for OTP
const otpSchema = Yup.object().shape({
  otpCode: Yup.string()
    .length(6, 'OTP must be 6 digits')
    .matches(/^\d{6}$/, 'OTP must contain only numbers')
    .required('OTP is required'),
});

export const Register: React.FC = () => {
  const [step, setStep] = useState<'email' | 'otp'>('email');
  const [email, setEmail] = useState('');
  const [name, setName] = useState('');
  const [password, setPassword] = useState('');
  const { effectiveTheme, toggleTheme } = useTheme();
  const [requestOTPMutation, { isLoading: isRequestingOTP, error: requestError }] = useRequestSignupOTPMutation();
  const [verifyOTPMutation, { isLoading: isVerifyingOTP, error: verifyError }] = useVerifySignupOTPMutation();
  const navigate = useNavigate();

  const handleEmailSubmit = async (values: RegisterForm) => {
    try {
      const result = await requestOTPMutation({ email: values.email }).unwrap();

      if (result.success) {
        setEmail(values.email);
        setName(values.name);
        setPassword(values.password);
        setStep('otp');
      }
    } catch (error) {
      // Error is handled by RTK Query and displayed via requestError
      console.error('Registration error:', error);
    }
  };

  const handleOTPSubmit = async (values: OTPVerifyForm) => {
    try {
      const result = await verifyOTPMutation({
        email,
        otpCode: values.otpCode,
        name: name,
        password: password
      }).unwrap();

      if (result.success && result.data) {
        // Tokens are already stored by the API transformation
        // Navigate to dashboard
        navigate('/dashboard');
      }
    } catch (error) {
      // Error is handled by RTK Query and displayed via verifyError
      console.error('OTP verification error:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        {/* Header */}
        <div className="flex justify-center">
          <div className="w-12 h-12 bg-teal-500 rounded-lg flex items-center justify-center shadow-lg">
            <Icon name="message" className="text-white" size={24} />
          </div>
        </div>
        <h2 className="mt-6 text-center text-3xl font-bold tracking-tight text-white">
          {step === 'email' ? 'Create your account' : 'Verify your email'}
        </h2>
        <p className="mt-2 text-center text-sm text-gray-400">
          {step === 'email' ? (
            <>
              Already have an account?{' '}
              <Link
                to="/login"
                className="font-medium text-teal-400 hover:text-teal-300 transition-colors"
              >
                Sign in
              </Link>
            </>
          ) : (
            <>
              We sent a 6-digit code to <strong className="text-white">{email}</strong>
            </>
          )}
        </p>

        {/* Theme Toggle */}
        <div className="flex justify-center mt-4">
          <Button
            variant="ghost"
            icon={effectiveTheme === 'dark' ? 'sun' : 'moon'}
            onClick={toggleTheme}
            size="small"
          >
            {effectiveTheme === 'dark' ? 'Light' : 'Dark'} Mode
          </Button>
        </div>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-gray-800 rounded-xl shadow-xl p-8 border border-gray-700">
          {step === 'email' ? (
            /* Email Registration Form */
            <Form
              initialValues={{
                email: '',
                name: '',
                password: '',
              }}
              validationSchema={registrationSchema}
              onSubmit={handleEmailSubmit}
              submitButtonText="Send Verification Code"
              loading={isRequestingOTP}
              errorComponent={() => (
                <div className="rounded-lg bg-red-900/20 border border-red-700 p-4">
                  <div className="flex items-start">
                    <Icon name="alert" className="text-red-400 mt-0.5 flex-shrink-0" size={16} />
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-red-400">
                        Registration Error
                      </h3>
                      <div className="mt-1 text-sm text-red-300">
                        <p>{(requestError as any)?.data?.error || 'Failed to send verification code. Please try again.'}</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              showFormErrors={!!requestError}
            >
              {/* Name Field */}
              <Input 
                name="name" 
                type="text" 
                label="Full Name" 
                placeholder="Enter your full name"
                required
                data-testid="name-input"
              />

              {/* Email Field */}
              <Input
                name="email"
                type="email"
                label="Email Address"
                placeholder="Enter your email address"
                required
                data-testid="email-input"
              />

              {/* Password Field */}
              <Input
                name="password"
                type="password"
                label="Password"
                placeholder="Enter your password"
                required
                data-testid="password-input"
              />
            </Form>
          ) : (
            /* OTP Verification Form */
            <div className="space-y-6">
              <div className="text-center">
                <p className="text-sm text-gray-400 mb-4">
                  Enter the 6-digit code we sent to your email
                </p>
                <Button
                  variant="ghost"
                  size="small"
                  onClick={() => setStep('email')}
                  className="text-teal-400 hover:text-teal-300"
                >
                  ← Change email address
                </Button>
              </div>

              <Form
                initialValues={{
                  otpCode: '',
                }}
                validationSchema={otpSchema}
                onSubmit={handleOTPSubmit}
                submitButtonText="Verify & Create Account"
                loading={isVerifyingOTP}
                errorComponent={() => (
                  <div className="rounded-lg bg-red-900/20 border border-red-700 p-4">
                    <div className="flex items-start">
                      <Icon name="alert" className="text-red-400 mt-0.5 flex-shrink-0" size={16} />
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-red-400">
                          Verification Error
                        </h3>
                        <div className="mt-1 text-sm text-red-300">
                          <p>{(verifyError as any)?.data?.error || 'Invalid verification code. Please try again.'}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
                showFormErrors={!!verifyError}
              >
                {/* OTP Code Field */}
                <Input 
                  name="otpCode" 
                  type="text" 
                  label="Verification Code" 
                  placeholder="Enter 6-digit code"
                  maxLength={6}
                  required
                  data-testid="otp-input"
                  className="text-center text-lg tracking-widest"
                />
              </Form>

              {/* Resend Code */}
              <div className="text-center">
                <p className="text-sm text-gray-400">
                  Didn't receive the code?{' '}
                  <Button
                    variant="ghost"
                    size="small"
                    type="button"
                    onClick={() => handleEmailSubmit({ email, name, password })}
                    disabled={isRequestingOTP}
                    className="font-medium text-teal-400 hover:text-teal-300 transition-colors disabled:opacity-50"
                  >
                    {isRequestingOTP ? 'Sending...' : 'Resend code'}
                  </Button>
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Register;
