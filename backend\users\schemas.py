# backend/users/schemas.py
from pydantic import BaseModel, EmailStr, Field, ConfigDict
from pydantic.alias_generators import to_camel
from typing import Optional
from datetime import datetime
import uuid

class UserBase(BaseModel):
    model_config = ConfigDict(
        alias_generator=to_camel,
        populate_by_name=True
    )

    email: EmailStr
    name: str = Field(..., min_length=1, max_length=100)

class UserCreate(UserBase):
    pass  # No password field for OTP-based signup

class UserLogin(BaseModel):
    email: EmailStr
    password: str

class UserResponse(UserBase):
    model_config = ConfigDict(
        from_attributes=True,
        alias_generator=to_camel,
        populate_by_name=True
    )

    id: uuid.UUID
    profile_picture: Optional[str] = None
    is_verified: bool
    last_seen: datetime
    created_at: datetime

class TokenResponse(BaseModel):
    access: str
    refresh: str

class AuthResponse(BaseModel):
    user: UserResponse
    tokens: TokenResponse

# OTP-related schemas
class OTPRequest(BaseModel):
    email: EmailStr
    purpose: str = Field(..., pattern="^(signup|login|password_reset)$")

class OTPVerify(BaseModel):
    email: EmailStr
    otp_code: str
    purpose: str = Field(..., pattern="^(signup|login|password_reset)$")
    name: Optional[str] = None  # Required for signup, optional for login
    password: Optional[str] = Field(None, min_length=8)  # Required for signup, optional for login

class OTPResponse(BaseModel):
    success: bool
    message: str
    expires_in_minutes: Optional[int] = None
