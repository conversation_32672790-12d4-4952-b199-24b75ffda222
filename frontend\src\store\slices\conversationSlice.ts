// frontend/src/store/slices/conversationSlice.ts
import { createSlice } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';

export interface Conversation {
  id: string;
  type: 'DIRECT' | 'GROUP';
  name?: string;
  participants: Array<{
    id: string;
    name: string;
    profilePicture?: string;
  }>;
  lastMessage?: {
    id: string;
    content: string;
    sender: {
      id: string;
      name: string;
      profilePicture?: string;
    };
    createdAt: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface DraftConversation {
  id: string; // temporary ID
  type: 'DIRECT' | 'GROUP';
  name?: string;
  participants: Array<{
    id: string;
    name: string;
    profilePicture?: string;
  }>;
  isDraft: true;
  createdAt: string;
  updatedAt: string;
  // Group-specific metadata
  description?: string;
  maxParticipants?: number;
  isPublic?: boolean;
}

export interface ConversationState {
  conversations: Conversation[];
  draftConversations: DraftConversation[];
  selectedConversationId: string | null;
  loading: boolean;
  error: string | null;
  creating: boolean;
}

const initialState: ConversationState = {
  conversations: [],
  draftConversations: [],
  selectedConversationId: null,
  loading: false,
  error: null,
  creating: false
};

// Helper function to generate unique draft IDs
const generateDraftId = (userId: string): string => {
  return `draft-${userId}-${Date.now()}`;
};

// Helper function to generate unique group draft IDs
const generateGroupDraftId = (): string => {
  return `draft-group-${Date.now()}`;
};

const conversationSlice = createSlice({
  name: 'conversations',
  initialState,
  reducers: {
    setSelectedConversation: (state, action: PayloadAction<string | null>) => {
      state.selectedConversationId = action.payload;
    },

    clearSelectedConversation: (state) => {
      state.selectedConversationId = null;
    },

    updateConversationLastMessage: (state, action: PayloadAction<{
      conversationId: string;
      message: {
        id: string;
        content: string;
        sender: { username: string };
        createdAt: string;
      };
    }>) => {
      const { conversationId, message } = action.payload;
      const conversation = state.conversations.find(c => c.id === conversationId);
      if (conversation) {
        conversation.lastMessage = message;
        conversation.updatedAt = message.createdAt;

        // Move conversation to top
        const index = state.conversations.indexOf(conversation);
        state.conversations.splice(index, 1);
        state.conversations.unshift(conversation);
      }
    },

    addConversation: (state, action: PayloadAction<Conversation>) => {
      const newConversation = action.payload;
      const existingIndex = state.conversations.findIndex(c => c.id === newConversation.id);

      if (existingIndex === -1) {
        state.conversations.unshift(newConversation);
      } else {
        state.conversations[existingIndex] = newConversation;
      }
    },

    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },

    // Draft conversation actions
    createDraftConversation: (state, action: PayloadAction<{
      userId: string;
      username: string;
      first_name: string;
      last_name: string;
      profile_picture?: string | null;
    }>) => {
      const { userId, username, first_name, last_name, profile_picture } = action.payload;
      const draftId = generateDraftId(userId);
      const now = new Date().toISOString();

      const participant = {
        id: userId,
        username,
        first_name,
        last_name,
        profile_picture: profile_picture || undefined,
      };

      const draftConversation: DraftConversation = {
        id: draftId,
        type: 'DIRECT',
        participants: [participant],
        isDraft: true,
        createdAt: now,
        updatedAt: now
      };

      // Remove any existing draft with the same user
      state.draftConversations = state.draftConversations.filter(
        draft => !draft.participants.some(p => p.id === userId)
      );

      // Add new draft to the beginning
      state.draftConversations.unshift(draftConversation);

      // Select the draft conversation
      state.selectedConversationId = draftId;
    },

    createDraftGroup: (state, action: PayloadAction<{
      name: string;
      description?: string;
      participants: Array<{
        id: string;
        username: string;
        first_name: string;
        last_name: string;
        profile_picture?: string;
      }>;
      maxParticipants?: number;
      isPublic?: boolean;
    }>) => {
      const { name, description, participants, maxParticipants, isPublic } = action.payload;
      const draftId = generateGroupDraftId();
      const now = new Date().toISOString();

      const draftConversation: DraftConversation = {
        id: draftId,
        type: 'GROUP',
        name,
        description,
        participants,
        maxParticipants,
        isPublic,
        isDraft: true,
        createdAt: now,
        updatedAt: now
      };

      // Add new draft to the beginning
      state.draftConversations.unshift(draftConversation);

      // Select the draft conversation
      state.selectedConversationId = draftId;
    },

    removeDraftConversation: (state, action: PayloadAction<string>) => {
      const draftId = action.payload;
      state.draftConversations = state.draftConversations.filter(
        draft => draft.id !== draftId
      );

      // Clear selection if the removed draft was selected
      if (state.selectedConversationId === draftId) {
        state.selectedConversationId = null;
      }
    },

    clearDraftConversations: (state) => {
      state.draftConversations = [];
    },

    convertDraftToRealConversation: (state, action: PayloadAction<{
      draftId: string;
      realConversation: Conversation;
    }>) => {
      const { draftId, realConversation } = action.payload;

      // Remove the draft
      state.draftConversations = state.draftConversations.filter(
        draft => draft.id !== draftId
      );

      // Add the real conversation
      const existingIndex = state.conversations.findIndex(c => c.id === realConversation.id);
      if (existingIndex === -1) {
        state.conversations.unshift(realConversation);
      } else {
        state.conversations[existingIndex] = realConversation;
      }

      // Update selected conversation ID
      if (state.selectedConversationId === draftId) {
        state.selectedConversationId = realConversation.id;
      }
    }
  }
});

export const {
  setSelectedConversation,
  clearSelectedConversation,
  updateConversationLastMessage,
  addConversation,
  setError,
  createDraftConversation,
  createDraftGroup,
  removeDraftConversation,
  clearDraftConversations,
  convertDraftToRealConversation
} = conversationSlice.actions;

export default conversationSlice.reducer;
