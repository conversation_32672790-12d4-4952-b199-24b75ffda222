// frontend/src/components/ui/messages/CallingMessage.tsx
import React from 'react';
import { Icon } from '../Icon';

/**
 * Call status types
 */
export type CallStatus = 
  | 'initiated'
  | 'answered'
  | 'declined'
  | 'missed'
  | 'ended'
  | 'failed'
  | 'busy';

/**
 * Call types
 */
export type CallType = 'audio' | 'video';

/**
 * Props for the CallingMessage component
 */
export interface CallingMessageProps {
  /** Type of call */
  callType: CallType;
  /** Status of the call */
  status: CallStatus;
  /** Duration of the call in seconds (for ended calls) */
  duration?: number;
  /** Whether this call was initiated by the current user */
  isOutgoing?: boolean;
  /** Timestamp of the call */
  timestamp: Date;
  /** Custom class name */
  className?: string;
  /** Click handler for call actions */
  onCallBack?: () => void;
}

/**
 * Format call duration
 */
const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
  return `${minutes}:${secs.toString().padStart(2, '0')}`;
};

/**
 * Enhanced CallingMessage component for call status messages
 * 
 * Features:
 * - Different call types (audio/video) with appropriate icons
 * - Call status indicators
 * - Duration display for completed calls
 * - Call back functionality
 * - Outgoing/incoming call distinction
 * - Glassmorphism design system integration
 * 
 * @example
 * ```tsx
 * <CallingMessage 
 *   callType="video"
 *   status="ended"
 *   duration={300}
 *   isOutgoing={true}
 *   timestamp={new Date()}
 *   onCallBack={() => initiateCall()}
 * />
 * ```
 */
export const CallingMessage: React.FC<CallingMessageProps> = ({
  callType,
  status,
  duration,
  isOutgoing = false,
  timestamp,
  className = '',
  onCallBack,
}) => {
  // Get call icon based on type and status
  const getCallIcon = (): string => {
    if (status === 'missed' || status === 'declined' || status === 'failed') {
      return isOutgoing ? 'phoneOff' : 'phoneMissed';
    }
    return callType === 'video' ? 'video' : 'phone';
  };

  // Get call status text
  const getStatusText = (): string => {
    switch (status) {
      case 'initiated':
        return isOutgoing ? 'Calling...' : 'Incoming call';
      case 'answered':
        return 'Call answered';
      case 'declined':
        return isOutgoing ? 'Call declined' : 'Call declined';
      case 'missed':
        return isOutgoing ? 'No answer' : 'Missed call';
      case 'ended':
        return duration ? `Call ended • ${formatDuration(duration)}` : 'Call ended';
      case 'failed':
        return 'Call failed';
      case 'busy':
        return 'Busy';
      default:
        return 'Call';
    }
  };

  // Get status color
  const getStatusColor = (): string => {
    switch (status) {
      case 'answered':
      case 'ended':
        return 'text-success';
      case 'declined':
      case 'missed':
      case 'failed':
        return 'text-danger';
      case 'busy':
        return 'text-warning';
      default:
        return 'text-text-secondary';
    }
  };

  const icon = getCallIcon();
  const statusText = getStatusText();
  const statusColor = getStatusColor();

  return (
    <div className={`flex justify-center my-4 ${className}`}>
      <div className="flex items-center max-w-sm px-4 py-3 rounded-lg glass-light border border-border-glass">
        {/* Call icon */}
        <div className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center ${
          status === 'missed' || status === 'declined' || status === 'failed'
            ? 'bg-danger/10'
            : status === 'answered' || status === 'ended'
            ? 'bg-success/10'
            : 'bg-primary/10'
        }`}>
          <Icon 
            name={icon} 
            className={
              status === 'missed' || status === 'declined' || status === 'failed'
                ? 'text-danger'
                : status === 'answered' || status === 'ended'
                ? 'text-success'
                : 'text-primary'
            } 
            size={20} 
          />
        </div>

        {/* Call info */}
        <div className="flex-1 ml-3">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-text-primary">
                {callType === 'video' ? 'Video call' : 'Voice call'}
              </p>
              <p className={`text-xs ${statusColor}`}>
                {statusText}
              </p>
            </div>

            {/* Call back button */}
            {onCallBack && (status === 'ended' || status === 'missed' || status === 'declined') && (
              <button
                onClick={onCallBack}
                className="ml-2 p-2 rounded-full bg-primary hover:bg-primary-hover text-white transition-colors"
                title={`Call back (${callType})`}
              >
                <Icon name={callType === 'video' ? 'video' : 'phone'} size={16} />
              </button>
            )}
          </div>

          {/* Timestamp */}
          <p className="text-xs text-text-tertiary mt-1">
            {timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </p>
        </div>
      </div>
    </div>
  );
};

export default CallingMessage;
