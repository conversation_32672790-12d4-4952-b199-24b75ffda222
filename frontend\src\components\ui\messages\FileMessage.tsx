// frontend/src/components/ui/messages/FileMessage.tsx
import React, { useCallback, useMemo } from 'react';
import { Icon } from '../Icon';

/**
 * File type information
 */
export interface FileInfo {
  /** File name */
  name: string;
  /** File size in bytes */
  size: number;
  /** File MIME type */
  type: string;
  /** File URL for download */
  url: string;
  /** File ID */
  id?: string;
}

/**
 * Props for the FileMessage component
 */
export interface FileMessageProps {
  /** File information */
  file: FileInfo;
  /** Whether this message is from the current user */
  isOwn?: boolean;
  /** Custom class name */
  className?: string;
  /** Click handler for the file */
  onClick?: () => void;
  /** Download handler */
  onDownload?: (file: FileInfo) => void;
  /** Preview handler for supported file types */
  onPreview?: (file: FileInfo) => void;
  /** Whether to show file preview */
  showPreview?: boolean;
  /** Whether the file is being downloaded */
  downloading?: boolean;
  /** Download progress (0-100) */
  downloadProgress?: number;
  /** Whether the file upload/send is in progress */
  uploading?: boolean;
  /** Upload progress (0-100) */
  uploadProgress?: number;
  /** Error state */
  error?: boolean;
  /** Error message */
  errorMessage?: string;
  /** Retry handler for failed uploads */
  onRetry?: () => void;
}

/**
 * File type to icon mapping
 */
const FILE_TYPE_ICONS: Record<string, string> = {
  // Documents
  'application/pdf': 'fileText',
  'application/msword': 'fileText',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'fileText',
  'application/vnd.ms-excel': 'fileText',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'fileText',
  'application/vnd.ms-powerpoint': 'fileText',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'fileText',
  'text/plain': 'fileText',
  'text/csv': 'fileText',
  
  // Images
  'image/jpeg': 'image',
  'image/jpg': 'image',
  'image/png': 'image',
  'image/gif': 'image',
  'image/webp': 'image',
  'image/svg+xml': 'image',
  
  // Audio
  'audio/mpeg': 'music',
  'audio/wav': 'music',
  'audio/ogg': 'music',
  'audio/mp3': 'music',
  
  // Video
  'video/mp4': 'video',
  'video/mpeg': 'video',
  'video/quicktime': 'video',
  'video/webm': 'video',
  
  // Archives
  'application/zip': 'archive',
  'application/x-rar-compressed': 'archive',
  'application/x-7z-compressed': 'archive',
  'application/gzip': 'archive',
  
  // Code
  'text/javascript': 'code',
  'text/html': 'code',
  'text/css': 'code',
  'application/json': 'code',
  'text/xml': 'code',
};

/**
 * Enhanced FileMessage component for displaying file attachments
 * 
 * Features:
 * - File type detection with appropriate icons
 * - File size formatting
 * - Download functionality with progress
 * - Upload progress display
 * - Preview support for supported file types
 * - Error handling with retry functionality
 * - Glassmorphism design system integration
 * - Accessibility support
 * 
 * @example
 * ```tsx
 * <FileMessage 
 *   file={{
 *     name: "document.pdf",
 *     size: 1024000,
 *     type: "application/pdf",
 *     url: "/files/document.pdf"
 *   }}
 *   isOwn={false}
 *   showPreview
 *   onDownload={(file) => downloadFile(file)}
 *   onPreview={(file) => previewFile(file)}
 * />
 * ```
 */
export const FileMessage: React.FC<FileMessageProps> = ({
  file,
  isOwn = false,
  className = '',
  onClick,
  onDownload,
  onPreview,
  showPreview = true,
  downloading = false,
  downloadProgress = 0,
  uploading = false,
  uploadProgress = 0,
  error = false,
  errorMessage,
  onRetry,
}) => {
  // Format file size
  const formatFileSize = useCallback((bytes: number): string => {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
  }, []);

  // Get file icon
  const getFileIcon = useCallback((mimeType: string): string => {
    return FILE_TYPE_ICONS[mimeType] || 'file';
  }, []);

  // Check if file can be previewed
  const canPreview = useMemo(() => {
    const previewableTypes = [
      'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
      'application/pdf',
      'text/plain', 'text/csv',
      'audio/mpeg', 'audio/wav', 'audio/ogg',
      'video/mp4', 'video/webm'
    ];
    return previewableTypes.includes(file.type);
  }, [file.type]);

  // Handle download
  const handleDownload = useCallback(() => {
    if (onDownload) {
      onDownload(file);
    } else {
      // Default download behavior
      const link = document.createElement('a');
      link.href = file.url;
      link.download = file.name;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }, [file, onDownload]);

  // Handle preview
  const handlePreview = useCallback(() => {
    if (onPreview && canPreview) {
      onPreview(file);
    }
  }, [file, onPreview, canPreview]);

  // Handle click
  const handleClick = useCallback(() => {
    if (onClick) {
      onClick();
    } else if (canPreview && showPreview) {
      handlePreview();
    } else {
      handleDownload();
    }
  }, [onClick, canPreview, showPreview, handlePreview, handleDownload]);

  // Container classes
  const containerClasses = [
    'group relative inline-block max-w-sm',
    isOwn ? 'ml-auto' : '',
    className,
  ].filter(Boolean).join(' ');

  // File card classes
  const cardClasses = [
    'flex items-center p-3 rounded-lg',
    'glass-light border border-border-glass',
    'transition-all duration-200',
    !error && !uploading ? 'hover:bg-bg-glass-heavy cursor-pointer' : '',
    error ? 'border-danger/50 bg-danger/5' : '',
  ].filter(Boolean).join(' ');

  const fileIcon = getFileIcon(file.type);
  const formattedSize = formatFileSize(file.size);
  const isInProgress = uploading || downloading;
  const progress = uploading ? uploadProgress : downloadProgress;

  return (
    <div className={containerClasses}>
      <div className={cardClasses} onClick={!error && !isInProgress ? handleClick : undefined}>
        {/* File icon */}
        <div className={`flex-shrink-0 w-10 h-10 rounded-lg flex items-center justify-center ${
          error ? 'bg-danger/10' : 'bg-primary/10'
        }`}>
          {isInProgress ? (
            <div className="relative w-6 h-6">
              <Icon name="spinner" className="animate-spin text-primary" size={16} />
            </div>
          ) : (
            <Icon 
              name={error ? 'alertTriangle' : fileIcon} 
              className={error ? 'text-danger' : 'text-primary'} 
              size={20} 
            />
          )}
        </div>

        {/* File info */}
        <div className="flex-1 min-w-0 ml-3">
          <div className="flex items-center justify-between">
            <p className="text-sm font-medium text-text-primary truncate">
              {file.name}
            </p>
            
            {/* Action buttons */}
            {!error && !isInProgress && (
              <div className="flex items-center space-x-1 ml-2 opacity-0 group-hover:opacity-100 transition-opacity">
                {canPreview && showPreview && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handlePreview();
                    }}
                    className="p-1 rounded hover:bg-bg-glass transition-colors"
                    title="Preview file"
                  >
                    <Icon name="eye" size={14} className="text-text-tertiary" />
                  </button>
                )}
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDownload();
                  }}
                  className="p-1 rounded hover:bg-bg-glass transition-colors"
                  title="Download file"
                >
                  <Icon name="download" size={14} className="text-text-tertiary" />
                </button>
              </div>
            )}
          </div>

          {/* File size and status */}
          <div className="flex items-center justify-between mt-1">
            <p className="text-xs text-text-secondary">
              {formattedSize}
            </p>

            {/* Progress or status */}
            {isInProgress && (
              <div className="flex items-center space-x-2">
                <div className="w-16 h-1 bg-bg-tertiary rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-primary transition-all duration-300"
                    style={{ width: `${progress}%` }}
                  />
                </div>
                <span className="text-xs text-text-secondary">
                  {Math.round(progress)}%
                </span>
              </div>
            )}

            {error && (
              <div className="flex items-center space-x-1">
                <span className="text-xs text-danger">
                  {errorMessage || 'Failed'}
                </span>
                {onRetry && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onRetry();
                    }}
                    className="text-xs text-primary hover:text-primary-hover underline"
                  >
                    Retry
                  </button>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default FileMessage;
