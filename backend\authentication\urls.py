# backend/authentication/urls.py
from django.urls import path
from . import views

urlpatterns = [
    # New OTP-based authentication endpoints
    path('signup/request-otp/', views.signup_request_otp, name='signup_request_otp'),
    path('signup/verify-otp/', views.signup_verify_otp, name='signup_verify_otp'),
    path('login/request-otp/', views.login_request_otp, name='login_request_otp'),
    path('login/verify-otp/', views.login_verify_otp, name='login_verify_otp'),
    
    # Legacy endpoints (deprecated)
    path('register/', views.register, name='register'),
    path('login/', views.login, name='login'),
    path('profile/', views.profile, name='profile'),
]
