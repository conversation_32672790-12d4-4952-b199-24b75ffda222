# backend/users/models.py
import uuid
from datetime import <PERSON><PERSON><PERSON>
from django.contrib.auth.models import AbstractUser, BaseUserManager
from django.db import models
from django.utils import timezone


class UserManager(BaseUserManager):
    """Custom user manager for email-based authentication"""
    
    def create_user(self, email, name=None, password=None, **extra_fields):
        """Create and return a regular user with an email and name."""
        if not email:
            raise ValueError('The Email field must be set')
        
        email = self.normalize_email(email)
        user = self.model(
            email=email,
            name=name or 'User',
            **extra_fields
        )
        
        if password:
            user.set_password(password)
        else:
            user.set_unusable_password()
            
        user.save(using=self._db)
        return user
    
    def create_superuser(self, email, name=None, password=None, **extra_fields):
        """Create and return a superuser with an email, name and password."""
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        
        if extra_fields.get('is_staff') is not True:
            raise ValueError('Superuser must have is_staff=True.')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('Superuser must have is_superuser=True.')
            
        return self.create_user(email, name, password, **extra_fields)


class User(AbstractUser):
    """Custom User model with email as primary identifier"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)  # Keep UUID
    email = models.EmailField(unique=True)
    name = models.CharField(max_length=100, default='User')  # Add default value
    profile_picture = models.URLField(blank=True, null=True)  # Use URLField instead of ImageField
    is_verified = models.BooleanField(default=False)  # Add verification status field
    is_online = models.BooleanField(default=False)
    last_seen = models.DateTimeField(auto_now=True)
    
    # Remove username requirement
    username = None
    first_name = None
    last_name = None
    
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['name']
    
    # Add the custom manager
    objects = UserManager()
    
    def __str__(self):
        return self.email
    
    class Meta:
        db_table = 'users_user'


class OTP(models.Model):
    """OTP model for email verification"""
    PURPOSES = [
        ('signup', 'Signup'),
        ('login', 'Login'),
        ('password_reset', 'Password Reset'),
    ]
    
    email = models.EmailField()
    code = models.CharField(max_length=6)
    purpose = models.CharField(max_length=20, choices=PURPOSES)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    is_used = models.BooleanField(default=False)
    
    class Meta:
        db_table = 'users_otp'
        ordering = ['-created_at']
    
    def save(self, *args, **kwargs):
        if not self.expires_at:
            # Set expiry to 10 minutes from creation
            self.expires_at = timezone.now() + timedelta(minutes=10)
        super().save(*args, **kwargs)
    
    def is_expired(self):
        return timezone.now() > self.expires_at
    
    def is_valid(self):
        return not self.is_used and not self.is_expired()
    
    @classmethod
    def generate_code(cls):
        """Generate a 6-digit OTP code"""
        return ''.join(secrets.choice(string.digits) for _ in range(6))
    
    def __str__(self):
        return f"OTP for {self.email} - {self.purpose}"


class OTPRateLimit(models.Model):
    """Rate limiting for OTP requests"""
    email = models.EmailField(unique=True)
    request_count = models.IntegerField(default=0)
    last_request = models.DateTimeField(auto_now=True)
    blocked_until = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'users_otp_rate_limit'
    
    def is_blocked(self):
        if self.blocked_until:
            return timezone.now() < self.blocked_until
        return False
    
    def reset_if_needed(self):
        """Reset counter if it's been more than an hour"""
        if timezone.now() - self.last_request > timedelta(hours=1):
            self.request_count = 0
            self.blocked_until = None
            self.save()
    
    def increment_request(self):
        """Increment request count and block if necessary"""
        self.request_count += 1
        self.last_request = timezone.now()
        
        # Block for 1 hour after 5 requests
        if self.request_count >= 5:
            self.blocked_until = timezone.now() + timedelta(hours=1)
        
        self.save()
    
    def __str__(self):
        return f"Rate limit for {self.email}"
