{"info": {"name": "ChatApp Phase 4 - Group Chat with Encryption API", "description": "Complete API collection for testing group chat functionality with end-to-end encryption, including group management, member permissions, and encrypted group messaging.", "version": "4.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://127.0.0.1:8000", "type": "string"}, {"key": "socket_url", "value": "ws://127.0.0.1:3001", "type": "string"}, {"key": "jwt_token", "value": "", "type": "string"}, {"key": "user_id", "value": "", "type": "string"}, {"key": "conversation_id", "value": "", "type": "string"}, {"key": "target_user_id", "value": "", "type": "string"}, {"key": "call_id", "value": "", "type": "string"}, {"key": "media_id", "value": "", "type": "string"}, {"key": "upload_id", "value": "", "type": "string"}, {"key": "download_token", "value": "", "type": "string"}, {"key": "message_id", "value": "", "type": "string"}, {"key": "test_email", "value": "<EMAIL>", "type": "string"}, {"key": "test_password", "value": "testpassword123", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "Login User", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has required fields', function () {", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('access');", "    pm.expect(response).to.have.property('refresh');", "    pm.expect(response).to.have.property('user');", "});", "", "pm.test('User object has required fields', function () {", "    const response = pm.response.json();", "    const user = response.user;", "    pm.expect(user).to.have.property('id');", "    pm.expect(user).to.have.property('email');", "    pm.expect(user).to.have.property('name');", "});", "", "// Store tokens for subsequent requests", "if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('access_token', response.access);", "    pm.environment.set('refresh_token', response.refresh);", "    pm.environment.set('user_id', response.user.id);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"testpass123\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/login/", "host": ["{{base_url}}"], "path": ["api", "auth", "login", ""]}}}, {"name": "Get User Profile", "event": [{"listen": "test", "script": {"exec": ["pm.test('Profile retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('id');", "    pm.expect(response).to.have.property('username');", "    pm.expect(response).to.have.property('email');", "    pm.expect(response).to.have.property('first_name');", "    pm.expect(response).to.have.property('last_name');", "});"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/auth/profile/", "host": ["{{base_url}}"], "path": ["api", "auth", "profile", ""]}}}, {"name": "Request OTP for Login", "event": [{"listen": "test", "script": {"exec": ["pm.test('OTP request successful', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('message');", "    pm.expect(response.message).to.include('OTP sent');", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{test_email}}\",\n  \"purpose\": \"login\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/login/request-otp/", "host": ["{{base_url}}"], "path": ["api", "auth", "login", "request-otp", ""]}}}, {"name": "Verify OTP for Login", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    // Store new token on successful OTP login", "    pm.collectionVariables.set('jwt_token', response.access);", "    pm.collectionVariables.set('user_id', response.user.id);", "    console.log('✅ OTP Login successful - JWT Token set:', response.access.substring(0, 20) + '...');", "    console.log('✅ User ID set:', response.user.id);", "    ", "    pm.test('O<PERSON> Login successful', function () {", "        pm.expect(response).to.have.property('access');", "        pm.expect(response).to.have.property('refresh');", "        pm.expect(response).to.have.property('user');", "    });", "} else {", "    pm.test('OTP verification failed', function () {", "        pm.response.to.have.status(400);", "        const response = pm.response.json();", "        pm.expect(response).to.have.property('error');", "    });", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{test_email}}\",\n  \"otp_code\": \"123456\",\n  \"purpose\": \"login\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/login/verify-otp/", "host": ["{{base_url}}"], "path": ["api", "auth", "login", "verify-otp", ""]}}}, {"name": "Request OTP for Signup", "event": [{"listen": "test", "script": {"exec": ["pm.test('OTP request successful', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('message');", "    pm.expect(response.message).to.include('OTP sent');", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"purpose\": \"signup\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/signup/request-otp/", "host": ["{{base_url}}"], "path": ["api", "auth", "signup", "request-otp", ""]}}}, {"name": "Verify OTP for Signup", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    // Store new token on successful OTP signup", "    pm.collectionVariables.set('jwt_token', response.access);", "    pm.collectionVariables.set('user_id', response.user.id);", "    console.log('✅ OTP Signup successful - JWT Token set:', response.access.substring(0, 20) + '...');", "    console.log('✅ User ID set:', response.user.id);", "    ", "    pm.test('OTP Signup successful', function () {", "        pm.expect(response).to.have.property('access');", "        pm.expect(response).to.have.property('refresh');", "        pm.expect(response).to.have.property('user');", "    });", "} else {", "    pm.test('OTP verification failed', function () {", "        pm.response.to.have.status(400);", "        const response = pm.response.json();", "        pm.expect(response).to.have.property('error');", "    });", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"otp_code\": \"123456\",\n  \"purpose\": \"signup\",\n  \"username\": \"newuser\",\n  \"password\": \"newpassword123\",\n  \"first_name\": \"New\",\n  \"last_name\": \"User\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/signup/verify-otp/", "host": ["{{base_url}}"], "path": ["api", "auth", "signup", "verify-otp", ""]}}}]}, {"name": "Messaging API", "item": [{"name": "List Conversations", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/messaging/conversations/", "host": ["{{base_url}}"], "path": ["api", "messaging", "conversations", ""]}}}, {"name": "Create Conversation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"DIRECT\",\n  \"participant_ids\": [\"{{user_id}}\"]\n}"}, "url": {"raw": "{{base_url}}/api/messaging/conversations/create/", "host": ["{{base_url}}"], "path": ["api", "messaging", "conversations", "create", ""]}}}, {"name": "Get Conversation Messages", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/messaging/conversations/{{conversation_id}}/messages/", "host": ["{{base_url}}"], "path": ["api", "messaging", "conversations", "{{conversation_id}}", "messages", ""]}}}, {"name": "Send Message", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"content\": \"Hello, this is a test message!\",\n  \"message_type\": \"TEXT\"\n}"}, "url": {"raw": "{{base_url}}/api/messaging/conversations/{{conversation_id}}/send/", "host": ["{{base_url}}"], "path": ["api", "messaging", "conversations", "{{conversation_id}}", "send", ""]}}}, {"name": "Search Users", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/messaging/users/search/?q=test", "host": ["{{base_url}}"], "path": ["api", "messaging", "users", "search", ""], "query": [{"key": "q", "value": "test"}]}}}]}, {"name": "Group Management API", "item": [{"name": "Create Group", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Test Group\",\n  \"description\": \"A test group for API testing\",\n  \"member_ids\": [],\n  \"max_participants\": 50,\n  \"is_public\": false\n}"}, "url": {"raw": "{{base_url}}/api/messaging/groups/create/", "host": ["{{base_url}}"], "path": ["api", "messaging", "groups", "create", ""]}}}, {"name": "Add Group Member", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_id\": \"{{user_id}}\",\n  \"role\": \"member\"\n}"}, "url": {"raw": "{{base_url}}/api/messaging/groups/{{conversation_id}}/add-member/", "host": ["{{base_url}}"], "path": ["api", "messaging", "groups", "{{conversation_id}}", "add-member", ""]}}}, {"name": "Remove Group Member", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/messaging/groups/{{conversation_id}}/remove-member/{{user_id}}/", "host": ["{{base_url}}"], "path": ["api", "messaging", "groups", "{{conversation_id}}", "remove-member", "{{user_id}}", ""]}}}, {"name": "Update Group Info", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Group Name\",\n  \"description\": \"Updated group description\"\n}"}, "url": {"raw": "{{base_url}}/api/messaging/groups/{{conversation_id}}/update/", "host": ["{{base_url}}"], "path": ["api", "messaging", "groups", "{{conversation_id}}", "update", ""]}}}, {"name": "Leave Group", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/messaging/groups/{{conversation_id}}/leave/", "host": ["{{base_url}}"], "path": ["api", "messaging", "groups", "{{conversation_id}}", "leave", ""]}}}]}, {"name": "Encryption API,Key Management", "item": [{"name": "Upload Key Bundle", "event": [{"listen": "test", "script": {"exec": ["pm.test('Key bundle uploaded successfully', function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('key_bundle_id');", "    pm.expect(response).to.have.property('message');", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"identity_public_key\": \"MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEVkpOnGmFntib9F/I6tIQmWeEDO186HPnDEKbTtMfiqAslpRFuBc6vvenIetWqyYJu/hXt25NdcJFoVAk2yr6zw==\",\n  \"signed_prekey_id\": 1,\n  \"signed_prekey_public\": \"MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE9o1LVOhahkUcLCAZgtF/2YpK8V7brrv0j5Au+B95HJ4lyDWAPMZfoEYJFYyE+7PaFG+lxo2B/jBch0aeTlI3dQ==\",\n  \"signed_prekey_signature\": \"MEUCIF4z5ltcLiOHEBq8ykORetLXOh4oD3yZjGtCKu8Rv/QSAiEAjK8Y4pOJYSGOPEX65j2XLK9IgkMjC7zRXMtUAvrUxEQ=\"\n}"}, "url": {"raw": "{{base_url}}/api/encryption/bundles/", "host": ["{{base_url}}"], "path": ["api", "encryption", "bundles", ""]}}}, {"name": "Get Key Bundle", "event": [{"listen": "test", "script": {"exec": ["pm.test('Key bundle retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('identity_public_key');", "    pm.expect(response).to.have.property('signed_prekey');", "});"]}}], "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/encryption/bundles/{{target_user_id}}/", "host": ["{{base_url}}"], "path": ["api", "encryption", "bundles", "{{target_user_id}}", ""]}}}, {"name": "Upload One-Time Pre-Keys", "event": [{"listen": "test", "script": {"exec": ["pm.test('One-time pre-keys uploaded successfully', function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('count');", "    pm.expect(response.count).to.equal(5);", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"prekeys\": [\n    {\n      \"key_id\": 1,\n      \"public_key\": \"MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE5yrcY99xmEbnRvXvZriCLjc2SyoGxrGuE23HNMNU4bEqwdg0f3Tf7Clf4kmYzQ9gummdQszpsLOsE+JVZs4JKw==\"\n    },\n    {\n      \"key_id\": 2,\n      \"public_key\": \"MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEyZ6+hgPsQd+SYQr3ftgZEeDI11y2c+kXfVUNgoOmSq8Rc+Y3bSFsNnQCzpUiYlAbiIlV7V/RFsolUfsny/yCSQ==\"\n    },\n    {\n      \"key_id\": 3,\n      \"public_key\": \"MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEpQr8kV7brrv0j5Au+B95HJ4lyDWAPMZfoEYJFYyE+7PaFG+lxo2B/jBch0aeTlI3dQyZ6+hgPsQd+SYQr3ftgZ==\"\n    },\n    {\n      \"key_id\": 4,\n      \"public_key\": \"MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEeDI11y2c+kXfVUNgoOmSq8Rc+Y3bSFsNnQCzpUiYlAbiIlV7V/RFsolUfsny/yCSQyrcY99xmEbnRvXvZriCLj==\"\n    },\n    {\n      \"key_id\": 5,\n      \"public_key\": \"MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEc2SyoGxrGuE23HNMNU4bEqwdg0f3Tf7Clf4kmYzQ9gummdQszpsLOsE+JVZs4JKwlpRFuBc6vvenIetWqyYJu/hX==\"\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/encryption/prekeys/", "host": ["{{base_url}}"], "path": ["api", "encryption", "prekeys", ""]}}}, {"name": "Get Pre-Key Count", "event": [{"listen": "test", "script": {"exec": ["pm.test('Pre-key count retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('available_count');", "    pm.expect(response).to.have.property('total_count');", "    pm.expect(response).to.have.property('used_count');", "});"]}}], "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/encryption/prekeys/count/", "host": ["{{base_url}}"], "path": ["api", "encryption", "prekeys", "count", ""]}}}]}, {"name": "Media API", "item": [{"name": "Upload Media (Simple)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Media uploaded successfully', function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('media_id');", "    pm.expect(response).to.have.property('message_id');", "    pm.expect(response).to.have.property('media_file');", "    pm.expect(response).to.have.property('message');", "    pm.collectionVariables.set('media_id', response.media_id);", "    pm.collectionVariables.set('message_id', response.message_id);", "});"]}}], "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "/path/to/test/file.jpg"}, {"key": "conversation_id", "value": "{{conversation_id}}", "type": "text"}, {"key": "wrapped_file_key", "value": "base64_encoded_wrapped_key", "type": "text"}, {"key": "file_nonce", "value": "base64_encoded_nonce", "type": "text"}, {"key": "file_hash", "value": "sha256_hash_of_file", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/media/upload/simple/", "host": ["{{base_url}}"], "path": ["api", "media", "upload", "simple", ""]}}}, {"name": "Upload Media (Chunked)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Chunked upload completed successfully', function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('media_id');", "    pm.expect(response).to.have.property('message_id');", "    pm.expect(response).to.have.property('media_file');", "    pm.expect(response).to.have.property('message');", "    pm.collectionVariables.set('media_id', response.media_id);", "    pm.collectionVariables.set('message_id', response.message_id);", "});"]}}], "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "chunk", "type": "file", "src": "/path/to/chunk/file"}, {"key": "conversation_id", "value": "{{conversation_id}}", "type": "text"}, {"key": "upload_session", "value": "unique_session_id", "type": "text"}, {"key": "chunk_number", "value": "1", "type": "text"}, {"key": "total_chunks", "value": "5", "type": "text"}, {"key": "chunk_hash", "value": "sha256_hash_of_chunk", "type": "text"}, {"key": "original_filename", "value": "large-file.mp4", "type": "text"}, {"key": "file_size", "value": "10485760", "type": "text"}, {"key": "mime_type", "value": "video/mp4", "type": "text"}, {"key": "wrapped_file_key", "value": "base64_encoded_wrapped_key", "type": "text"}, {"key": "file_nonce", "value": "base64_encoded_nonce", "type": "text"}, {"key": "file_hash", "value": "sha256_hash_of_complete_file", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/media/upload/chunked/", "host": ["{{base_url}}"], "path": ["api", "media", "upload", "chunked", ""]}}}, {"name": "Download Media by ID", "event": [{"listen": "test", "script": {"exec": ["pm.test('Media downloaded successfully', function () {", "    pm.response.to.have.status(200);", "    pm.expect(pm.response.headers.get('Content-Type')).to.exist;", "    pm.expect(pm.response.headers.get('Content-Length')).to.exist;", "});"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/media/download/{{media_id}}/", "host": ["{{base_url}}"], "path": ["api", "media", "download", "{{media_id}}", ""]}}}, {"name": "Download Media by Token", "event": [{"listen": "test", "script": {"exec": ["pm.test('Media downloaded via token successfully', function () {", "    pm.response.to.have.status(200);", "    pm.expect(pm.response.headers.get('Content-Type')).to.exist;", "});"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/media/download/{{download_token}}/", "host": ["{{base_url}}"], "path": ["api", "media", "download", "{{download_token}}", ""]}}}, {"name": "Get Media Thumbnail", "event": [{"listen": "test", "script": {"exec": ["pm.test('Thumbnail retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    pm.expect(pm.response.headers.get('Content-Type')).to.include('image');", "});"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/media/thumbnail/{{media_id}}/?size=150", "host": ["{{base_url}}"], "path": ["api", "media", "thumbnail", "{{media_id}}", ""], "query": [{"key": "size", "value": "150", "description": "Thumbnail size in pixels (optional, default: 150)"}]}}}]}, {"name": "Messaging API", "item": [{"name": "List Conversations", "event": [{"listen": "test", "script": {"exec": ["pm.test('Conversations retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('results');", "    pm.expect(response.results).to.be.an('array');", "    if (response.results.length > 0) {", "        const conversation = response.results[0];", "        pm.expect(conversation).to.have.property('id');", "        pm.expect(conversation).to.have.property('type');", "        pm.expect(conversation).to.have.property('participants');", "        pm.expect(conversation).to.have.property('is_encrypted');", "    }", "});"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/messaging/conversations/?page=1&page_size=20", "host": ["{{base_url}}"], "path": ["api", "messaging", "conversations", ""], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "20"}]}}}, {"name": "Create Direct Conversation", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201 || pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('conversation_id', response.id);", "    console.log('Conversation ID set:', response.id);", "    pm.test('Conversation created or retrieved', function () {", "        pm.expect(response).to.have.property('id');", "        pm.expect(response).to.have.property('type');", "        pm.expect(response.type).to.equal('DIRECT');", "        pm.expect(response).to.have.property('participants');", "    });", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"DIRECT\",\n  \"participant_ids\": [\"{{target_user_id}}\"]\n}"}, "url": {"raw": "{{base_url}}/api/messaging/conversations/create/", "host": ["{{base_url}}"], "path": ["api", "messaging", "conversations", "create", ""]}}}, {"name": "Create Group Conversation", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.test('Group conversation created', function () {", "        pm.expect(response).to.have.property('id');", "        pm.expect(response).to.have.property('type');", "        pm.expect(response.type).to.equal('GROUP');", "        pm.expect(response).to.have.property('name');", "        pm.expect(response).to.have.property('participants');", "    });", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"GROUP\",\n  \"name\": \"Test Group Chat\",\n  \"participant_ids\": [\"{{target_user_id}}\"]\n}"}, "url": {"raw": "{{base_url}}/api/messaging/conversations/create/", "host": ["{{base_url}}"], "path": ["api", "messaging", "conversations", "create", ""]}}}, {"name": "Get Conversation Messages", "event": [{"listen": "test", "script": {"exec": ["pm.test('Messages retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('results');", "    pm.expect(response.results).to.be.an('array');", "    if (response.results.length > 0) {", "        const message = response.results[0];", "        pm.expect(message).to.have.property('id');", "        pm.expect(message).to.have.property('sender');", "        pm.expect(message).to.have.property('message_type');", "        pm.expect(message).to.have.property('is_encrypted');", "        pm.expect(message).to.have.property('created_at');", "    }", "});"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/messaging/conversations/{{conversation_id}}/messages/?page=1&page_size=50", "host": ["{{base_url}}"], "path": ["api", "messaging", "conversations", "{{conversation_id}}", "messages", ""], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}]}}}, {"name": "Send Plaintext Message", "event": [{"listen": "test", "script": {"exec": ["pm.test('Plaintext message sent successfully', function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('id');", "    pm.expect(response).to.have.property('content');", "    pm.expect(response.is_encrypted).to.be.false;", "    pm.expect(response.message_type).to.equal('TEXT');", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"content\": \"Hello! This is a plaintext message for testing.\",\n  \"message_type\": \"TEXT\"\n}"}, "url": {"raw": "{{base_url}}/api/messaging/conversations/{{conversation_id}}/send/", "host": ["{{base_url}}"], "path": ["api", "messaging", "conversations", "{{conversation_id}}", "send", ""]}}}, {"name": "Send Encrypted Message", "event": [{"listen": "test", "script": {"exec": ["pm.test('Encrypted message sent successfully', function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('id');", "    pm.expect(response).to.have.property('encrypted_content');", "    pm.expect(response).to.have.property('iv');", "    pm.expect(response).to.have.property('sender_ratchet_key');", "    pm.expect(response.is_encrypted).to.be.true;", "    pm.expect(response.message_type).to.equal('TEXT');", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"encrypted_content\": \"dGhpcyBpcyBmYWtlIGVuY3J5cHRlZCBjb250ZW50\",\n  \"iv\": \"cmFuZG9tSVY5NmJpdA==\",\n  \"sender_ratchet_key\": \"ZmFrZVJhdGNoZXRLZXlTUEtJRm9ybWF0\",\n  \"message_number\": 1,\n  \"previous_chain_length\": 0,\n  \"message_type\": \"TEXT\"\n}"}, "url": {"raw": "{{base_url}}/api/messaging/conversations/{{conversation_id}}/send/", "host": ["{{base_url}}"], "path": ["api", "messaging", "conversations", "{{conversation_id}}", "send", ""]}}}, {"name": "Get Conversation Encryption Status", "event": [{"listen": "test", "script": {"exec": ["pm.test('Encryption status retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('conversation_id');", "    pm.expect(response).to.have.property('is_encrypted');", "    pm.expect(response).to.have.property('participants');", "    pm.expect(response.participants).to.be.an('array');", "    if (response.participants.length > 0) {", "        const participant = response.participants[0];", "        pm.expect(participant).to.have.property('id');", "        pm.expect(participant).to.have.property('username');", "        pm.expect(participant).to.have.property('has_encryption');", "    }", "});"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/messaging/conversations/{{conversation_id}}/encryption-status/", "host": ["{{base_url}}"], "path": ["api", "messaging", "conversations", "{{conversation_id}}", "encryption-status", ""]}}}, {"name": "Search Users", "event": [{"listen": "test", "script": {"exec": ["pm.test('User search completed successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('success');", "    pm.expect(response).to.have.property('results');", "    pm.expect(response.results).to.be.an('array');", "    if (response.results.length > 0) {", "        const user = response.results[0];", "        pm.expect(user).to.have.property('id');", "        pm.expect(user).to.have.property('username');", "        pm.expect(user).to.have.property('first_name');", "        pm.expect(user).to.have.property('last_name');", "        pm.expect(user).to.have.property('full_name');", "        // Set target_user_id for other tests", "        pm.collectionVariables.set('target_user_id', user.id);", "    }", "});"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/messaging/users/search/?q=test", "host": ["{{base_url}}"], "path": ["api", "messaging", "users", "search", ""], "query": [{"key": "q", "value": "test", "description": "Search query (minimum 2 characters)"}]}}}, {"name": "Get User Profile", "event": [{"listen": "test", "script": {"exec": ["pm.test('User profile retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('success');", "    pm.expect(response).to.have.property('data');", "    const user = response.data;", "    pm.expect(user).to.have.property('id');", "    pm.expect(user).to.have.property('username');", "    pm.expect(user).to.have.property('first_name');", "    pm.expect(user).to.have.property('last_name');", "    pm.expect(user).to.have.property('full_name');", "});"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/messaging/users/{{target_user_id}}/", "host": ["{{base_url}}"], "path": ["api", "messaging", "users", "{{target_user_id}}", ""]}}}]}, {"name": "Phase 4: Group Chat Management", "item": [{"name": "Create Group", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Test Group\",\n  \"description\": \"A test group for Phase 4\",\n  \"member_ids\": [\"{{target_user_id}}\"],\n  \"max_participants\": 50,\n  \"is_public\": false\n}"}, "url": {"raw": "{{base_url}}/api/messaging/groups/create/", "host": ["{{base_url}}"], "path": ["api", "messaging", "groups", "create", ""]}, "description": "Create a new group conversation with initial members"}}, {"name": "Add Group Member", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_id\": \"{{target_user_id}}\",\n  \"role\": \"member\"\n}"}, "url": {"raw": "{{base_url}}/api/messaging/groups/{{conversation_id}}/add-member/", "host": ["{{base_url}}"], "path": ["api", "messaging", "groups", "{{conversation_id}}", "add-member", ""]}, "description": "Add a new member to an existing group"}}, {"name": "Remove Group Member", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/messaging/groups/{{conversation_id}}/remove-member/{{target_user_id}}/", "host": ["{{base_url}}"], "path": ["api", "messaging", "groups", "{{conversation_id}}", "remove-member", "{{target_user_id}}", ""]}, "description": "Remove a member from a group (admin only)"}}, {"name": "Update Group Info", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Group Name\",\n  \"description\": \"Updated group description\",\n  \"avatar_url\": \"https://example.com/avatar.jpg\"\n}"}, "url": {"raw": "{{base_url}}/api/messaging/groups/{{conversation_id}}/update/", "host": ["{{base_url}}"], "path": ["api", "messaging", "groups", "{{conversation_id}}", "update", ""]}, "description": "Update group information (name, description, avatar)"}}, {"name": "Leave Group", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/messaging/groups/{{conversation_id}}/leave/", "host": ["{{base_url}}"], "path": ["api", "messaging", "groups", "{{conversation_id}}", "leave", ""]}, "description": "Leave a group conversation"}}]}, {"name": "Phase 4: Group Encryption", "item": [{"name": "Get Group Keys", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/messaging/groups/{{conversation_id}}/keys/", "host": ["{{base_url}}"], "path": ["api", "messaging", "groups", "{{conversation_id}}", "keys", ""]}, "description": "Get unclaimed group encryption keys for offline member"}}, {"name": "Claim Group Key", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"key_id\": \"group-key-uuid-here\"\n}"}, "url": {"raw": "{{base_url}}/api/messaging/groups/{{conversation_id}}/claim-key/", "host": ["{{base_url}}"], "path": ["api", "messaging", "groups", "{{conversation_id}}", "claim-key", ""]}, "description": "Mark a group key as claimed by the user"}}, {"name": "Send Group Message", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"content\": \"Hello group! This is an encrypted message.\",\n  \"sender_private_key\": \"base64-encoded-private-key-for-signing\"\n}"}, "url": {"raw": "{{base_url}}/api/messaging/groups/{{conversation_id}}/send-message/", "host": ["{{base_url}}"], "path": ["api", "messaging", "groups", "{{conversation_id}}", "send-message", ""]}, "description": "Send a message to a group with signature for authentication"}}, {"name": "Verify Message Signature", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/messaging/messages/{{message_id}}/verify-signature/", "host": ["{{base_url}}"], "path": ["api", "messaging", "messages", "{{message_id}}", "verify-signature", ""]}, "description": "Verify the ECDSA signature of a group message"}}, {"name": "Rotate Group Keys (Manual)", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/messaging/groups/{{conversation_id}}/rotate-keys/", "host": ["{{base_url}}"], "path": ["api", "messaging", "groups", "{{conversation_id}}", "rotate-keys", ""]}, "description": "Manually rotate group encryption keys (admin only)"}}]}, {"name": "Calling API", "item": [{"name": "Initiate Call", "event": [{"listen": "test", "script": {"exec": ["pm.test('Call initiated successfully', function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('call_id');", "    pm.expect(response).to.have.property('status');", "    pm.expect(response.status).to.equal('INITIATED');", "    pm.collectionVariables.set('call_id', response.call_id);", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"target_user_id\": \"{{target_user_id}}\",\n  \"call_type\": \"AUDIO\",\n  \"conversation_id\": \"{{conversation_id}}\"\n}"}, "url": {"raw": "{{base_url}}/api/calling/initiate/", "host": ["{{base_url}}"], "path": ["api", "calling", "initiate", ""]}}}, {"name": "Answer Call", "event": [{"listen": "test", "script": {"exec": ["pm.test('Call answered successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('status');", "    pm.expect(response.status).to.equal('ACTIVE');", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"call_id\": \"{{call_id}}\"\n}"}, "url": {"raw": "{{base_url}}/api/calling/{{call_id}}/answer/", "host": ["{{base_url}}"], "path": ["api", "calling", "{{call_id}}", "answer", ""]}}}, {"name": "Decline Call", "event": [{"listen": "test", "script": {"exec": ["pm.test('Call declined successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('status');", "    pm.expect(response.status).to.equal('DECLINED');", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"call_id\": \"{{call_id}}\",\n  \"reason\": \"User declined\"\n}"}, "url": {"raw": "{{base_url}}/api/calling/{{call_id}}/decline/", "host": ["{{base_url}}"], "path": ["api", "calling", "{{call_id}}", "decline", ""]}}}, {"name": "End Call", "event": [{"listen": "test", "script": {"exec": ["pm.test('Call ended successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('status');", "    pm.expect(response.status).to.equal('ENDED');", "    pm.expect(response).to.have.property('duration');", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"call_id\": \"{{call_id}}\"\n}"}, "url": {"raw": "{{base_url}}/api/calling/{{call_id}}/end/", "host": ["{{base_url}}"], "path": ["api", "calling", "{{call_id}}", "end", ""]}}}, {"name": "Update SDP Offer", "event": [{"listen": "test", "script": {"exec": ["pm.test('SDP offer updated successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('success');", "    pm.expect(response.success).to.be.true;", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"call_id\": \"{{call_id}}\",\n  \"sdp_offer\": \"v=0\\r\\no=- 123456789 123456789 IN IP4 0.0.0.0\\r\\ns=-\\r\\nt=0 0\\r\\na=group:BUNDLE 0\\r\\na=msid-semantic: WMS\\r\\nm=audio 9 UDP/TLS/RTP/SAVPF 111\\r\\nc=IN IP4 0.0.0.0\\r\\na=rtcp:9 IN IP4 0.0.0.0\\r\\na=ice-ufrag:test\\r\\na=ice-pwd:test\\r\\na=fingerprint:sha-256 00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00\\r\\na=setup:actpass\\r\\na=mid:0\\r\\na=extmap:1 urn:ietf:params:rtp-hdrext:ssrc-audio-level\\r\\na=sendrecv\\r\\na=msid:stream track\\r\\na=rtcp-mux\\r\\na=rtpmap:111 opus/48000/2\\r\\na=ssrc:123456789 cname:test\\r\\n\"\n}"}, "url": {"raw": "{{base_url}}/api/calling/{{call_id}}/sdp/", "host": ["{{base_url}}"], "path": ["api", "calling", "{{call_id}}", "sdp", ""]}}}, {"name": "Update SDP Answer", "event": [{"listen": "test", "script": {"exec": ["pm.test('SDP answer updated successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('success');", "    pm.expect(response.success).to.be.true;", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"call_id\": \"{{call_id}}\",\n  \"sdp_answer\": \"v=0\\r\\no=- 987654321 987654321 IN IP4 0.0.0.0\\r\\ns=-\\r\\nt=0 0\\r\\na=group:BUNDLE 0\\r\\na=msid-semantic: WMS\\r\\nm=audio 9 UDP/TLS/RTP/SAVPF 111\\r\\nc=IN IP4 0.0.0.0\\r\\na=rtcp:9 IN IP4 0.0.0.0\\r\\na=ice-ufrag:test2\\r\\na=ice-pwd:test2\\r\\na=fingerprint:sha-256 11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11\\r\\na=setup:active\\r\\na=mid:0\\r\\na=extmap:1 urn:ietf:params:rtp-hdrext:ssrc-audio-level\\r\\na=sendrecv\\r\\na=msid:stream track\\r\\na=rtcp-mux\\r\\na=rtpmap:111 opus/48000/2\\r\\na=ssrc:987654321 cname:test2\\r\\n\"\n}"}, "url": {"raw": "{{base_url}}/api/calling/{{call_id}}/sdp/", "host": ["{{base_url}}"], "path": ["api", "calling", "{{call_id}}", "sdp", ""]}}}, {"name": "Submit Quality Report", "event": [{"listen": "test", "script": {"exec": ["pm.test('Quality report submitted successfully', function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('success');", "    pm.expect(response.success).to.be.true;", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"call_id\": \"{{call_id}}\",\n  \"audio_quality\": 4,\n  \"video_quality\": 3,\n  \"connection_quality\": 5,\n  \"overall_rating\": 4,\n  \"issues\": [\"audio_delay\", \"video_pixelation\"],\n  \"comments\": \"Good call overall, minor audio delay\"\n}"}, "url": {"raw": "{{base_url}}/api/calling/{{call_id}}/quality/", "host": ["{{base_url}}"], "path": ["api", "calling", "{{call_id}}", "quality", ""]}}}, {"name": "Get Call History", "event": [{"listen": "test", "script": {"exec": ["pm.test('Call history retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('results');", "    pm.expect(response.results).to.be.an('array');", "    if (response.results.length > 0) {", "        const call = response.results[0];", "        pm.expect(call).to.have.property('id');", "        pm.expect(call).to.have.property('caller');", "        pm.expect(call).to.have.property('callee');", "        pm.expect(call).to.have.property('status');", "        pm.expect(call).to.have.property('call_type');", "        pm.expect(call).to.have.property('started_at');", "    }", "});"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/calling/history/?page=1&page_size=20", "host": ["{{base_url}}"], "path": ["api", "calling", "history", ""], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "20"}]}}}, {"name": "Get Call Details", "event": [{"listen": "test", "script": {"exec": ["pm.test('Call details retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('id');", "    pm.expect(response).to.have.property('caller');", "    pm.expect(response).to.have.property('callee');", "    pm.expect(response).to.have.property('status');", "    pm.expect(response).to.have.property('call_type');", "    pm.expect(response).to.have.property('started_at');", "    pm.expect(response).to.have.property('duration');", "});"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/calling/details/{{call_id}}/", "host": ["{{base_url}}"], "path": ["api", "calling", "details", "{{call_id}}", ""]}}}]}, {"name": "WebSocket Examples", "item": [{"name": "Socket Connection Example", "request": {"method": "GET", "header": [], "url": {"raw": "{{socket_url}}", "host": ["{{socket_url}}"]}, "description": "WebSocket connection example. Use this URL with a WebSocket client.\n\nConnection payload:\n```json\n{\n  \"auth\": {\n    \"token\": \"{{jwt_token}}\"\n  }\n}\n```"}}, {"name": "Send Encrypted Message via Socket", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"event\": \"send_message\",\n  \"data\": {\n    \"conversationId\": \"{{conversation_id}}\",\n    \"tempId\": \"temp-123\",\n    \"encryptedContent\": \"dGhpcyBpcyBmYWtlIGVuY3J5cHRlZCBjb250ZW50\",\n    \"iv\": \"cmFuZG9tSVY5NmJpdA==\",\n    \"senderRatchetKey\": \"ZmFrZVJhdGNoZXRLZXlTUEtJRm9ybWF0\",\n    \"messageNumber\": 1,\n    \"previousChainLength\": 0,\n    \"messageType\": \"TEXT\"\n  }\n}"}, "url": {"raw": "{{base_url}}/websocket-example", "host": ["{{base_url}}"], "path": ["websocket-example"]}, "description": "This is a documentation example. Use a WebSocket client to emit this event:\n\nEvent: `send_message`\nData: See request body"}}, {"name": "Key Exchange Request via Socket", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"event\": \"key_exchange_request\",\n  \"data\": {\n    \"targetUserId\": \"{{target_user_id}}\",\n    \"conversationId\": \"{{conversation_id}}\",\n    \"ephemeralPublicKey\": \"ZmFrZUVwaGVtZXJhbEtleVNQS0k=\"\n  }\n}"}, "url": {"raw": "{{base_url}}/websocket-example", "host": ["{{base_url}}"], "path": ["websocket-example"]}, "description": "This is a documentation example. Use a WebSocket client to emit this event:\n\nEvent: `key_exchange_request`\nData: See request body\n\nExpected response event: `key_exchange_response`"}}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Enhanced token management for all requests", "const token = pm.collectionVariables.get('jwt_token');", "const requestName = pm.info.requestName;", "", "// Skip auth for login/register endpoints", "const skipAuthEndpoints = ['Login User', 'Register User'];", "if (skipAuthEndpoints.includes(requestName)) {", "    console.log('🔓 Skipping auth for:', requestName);", "    return;", "}", "", "// Auto-set authorization header if jwt_token is available", "if (token && !pm.request.headers.has('Authorization')) {", "    pm.request.headers.add({", "        key: 'Authorization',", "        value: `Bear<PERSON> ${token}`", "    });", "    console.log('🔐 Authorization header set for:', requestName);", "} else if (!token) {", "    console.log('⚠️ No JWT token available for:', requestName);", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Enhanced global error handling and token management", "const requestName = pm.info.requestName;", "const skipAuthEndpoints = ['Login User', 'Register User'];", "", "// Handle authentication errors globally", "if (pm.response.code === 401 || pm.response.code === 403) {", "    if (!skipAuthEndpoints.includes(requestName)) {", "        console.log('🚫 Authentication failed for:', requestName);", "        console.log('🧹 Clearing invalid tokens...');", "        pm.collectionVariables.unset('jwt_token');", "        pm.collectionVariables.unset('user_id');", "        ", "        pm.test('Authentication required - tokens cleared', function () {", "            pm.expect(pm.response.code).to.be.oneOf([401, 403]);", "            pm.expect(pm.collectionVariables.get('jwt_token')).to.be.undefined;", "        });", "    }", "} else {", "    // Standard tests for successful requests", "    if (!skipAuthEndpoints.includes(requestName)) {", "        pm.test('No authentication errors', function () {", "            pm.expect(pm.response.code).to.not.be.oneOf([401, 403]);", "        });", "    }", "}", "", "pm.test('No server errors', function () {", "    pm.expect(pm.response.code).to.be.below(500);", "});", "", "// Log response for debugging", "console.log(`📊 ${requestName}: ${pm.response.code} ${pm.response.status}`);"]}}]}