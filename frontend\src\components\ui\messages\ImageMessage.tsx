// frontend/src/components/ui/messages/ImageMessage.tsx
import React, { useState, useCallback } from 'react';
import { Icon } from '../Icon';

/**
 * Props for the ImageMessage component
 */
export interface ImageMessageProps {
  /** The image URL */
  src: string;
  /** Alternative text for the image */
  alt?: string;
  /** Optional caption for the image */
  caption?: string;
  /** Whether this message is from the current user */
  isOwn?: boolean;
  /** Custom class name */
  className?: string;
  /** Click handler for the image */
  onClick?: () => void;
  /** Whether to enable lightbox functionality */
  enableLightbox?: boolean;
  /** Maximum width for the image */
  maxWidth?: number;
  /** Maximum height for the image */
  maxHeight?: number;
  /** Whether to show download button */
  showDownload?: boolean;
  /** Download handler */
  onDownload?: () => void;
  /** Loading state */
  loading?: boolean;
  /** Error state */
  error?: boolean;
  /** Retry handler for failed images */
  onRetry?: () => void;
}

/**
 * Enhanced ImageMessage component for displaying image messages
 * 
 * Features:
 * - Image loading states with skeleton
 * - Error handling with retry functionality
 * - Lightbox functionality for full-screen viewing
 * - Image download capability
 * - Caption support
 * - Responsive sizing with aspect ratio preservation
 * - Glassmorphism design system integration
 * - Accessibility support
 * 
 * @example
 * ```tsx
 * <ImageMessage 
 *   src="/path/to/image.jpg"
 *   alt="Shared image"
 *   caption="Check out this photo!"
 *   isOwn={false}
 *   enableLightbox
 *   showDownload
 *   onDownload={() => downloadImage()}
 * />
 * ```
 */
export const ImageMessage: React.FC<ImageMessageProps> = ({
  src,
  alt = 'Shared image',
  caption,
  isOwn = false,
  className = '',
  onClick,
  enableLightbox = true,
  maxWidth = 300,
  maxHeight = 400,
  showDownload = true,
  onDownload,
  loading = false,
  error = false,
  onRetry,
}) => {
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);
  const [showLightbox, setShowLightbox] = useState(false);

  // Handle image load
  const handleImageLoad = useCallback(() => {
    setImageLoading(false);
    setImageError(false);
  }, []);

  // Handle image error
  const handleImageError = useCallback(() => {
    setImageLoading(false);
    setImageError(true);
  }, []);

  // Handle image click
  const handleImageClick = useCallback(() => {
    if (onClick) {
      onClick();
    } else if (enableLightbox && !imageError && !imageLoading) {
      setShowLightbox(true);
    }
  }, [onClick, enableLightbox, imageError, imageLoading]);

  // Handle download
  const handleDownload = useCallback(async () => {
    if (onDownload) {
      onDownload();
      return;
    }

    try {
      const response = await fetch(src);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `image-${Date.now()}.jpg`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to download image:', error);
    }
  }, [src, onDownload]);

  // Handle retry
  const handleRetry = useCallback(() => {
    setImageError(false);
    setImageLoading(true);
    if (onRetry) {
      onRetry();
    }
  }, [onRetry]);

  // Container classes
  const containerClasses = [
    'group relative inline-block',
    'rounded-lg overflow-hidden',
    'transition-all duration-200',
    isOwn ? 'ml-auto' : '',
    className,
  ].filter(Boolean).join(' ');

  // Image container classes
  const imageContainerClasses = [
    'relative',
    'glass-light border border-border-glass',
    'rounded-lg overflow-hidden',
    enableLightbox && !imageError && !imageLoading ? 'cursor-pointer hover:scale-[1.02]' : '',
  ].filter(Boolean).join(' ');

  const isLoading = loading || imageLoading;
  const hasError = error || imageError;

  return (
    <>
      <div className={containerClasses} style={{ maxWidth }}>
        <div className={imageContainerClasses}>
          {/* Loading skeleton */}
          {isLoading && (
            <div 
              className="flex items-center justify-center bg-bg-tertiary animate-pulse"
              style={{ width: maxWidth, height: Math.min(maxHeight, 200) }}
            >
              <Icon name="image" className="text-text-tertiary" size={48} />
            </div>
          )}

          {/* Error state */}
          {hasError && (
            <div 
              className="flex flex-col items-center justify-center bg-bg-tertiary text-text-secondary p-8 space-y-2"
              style={{ width: maxWidth, height: Math.min(maxHeight, 200) }}
            >
              <Icon name="imageOff" className="text-text-tertiary" size={48} />
              <p className="text-sm text-center">Failed to load image</p>
              {onRetry && (
                <button
                  onClick={handleRetry}
                  className="text-xs text-primary hover:text-primary-hover underline"
                >
                  Try again
                </button>
              )}
            </div>
          )}

          {/* Image */}
          {!hasError && (
            <img
              src={src}
              alt={alt}
              className={`block w-full h-auto object-cover transition-opacity duration-200 ${
                isLoading ? 'opacity-0' : 'opacity-100'
              }`}
              style={{ maxWidth, maxHeight }}
              onLoad={handleImageLoad}
              onError={handleImageError}
              onClick={handleImageClick}
            />
          )}

          {/* Image overlay actions */}
          {!hasError && !isLoading && (
            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors">
              <div className="absolute top-2 right-2 flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                {showDownload && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDownload();
                    }}
                    className="p-1.5 bg-black/50 text-white rounded-full hover:bg-black/70 transition-colors"
                    title="Download image"
                  >
                    <Icon name="download" size={14} />
                  </button>
                )}
                {enableLightbox && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setShowLightbox(true);
                    }}
                    className="p-1.5 bg-black/50 text-white rounded-full hover:bg-black/70 transition-colors"
                    title="View full size"
                  >
                    <Icon name="maximize" size={14} />
                  </button>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Caption */}
        {caption && (
          <div className={`mt-2 px-3 py-2 rounded-lg text-sm ${
            isOwn 
              ? 'bg-primary/10 text-text-primary' 
              : 'glass-light text-text-primary'
          }`}>
            {caption}
          </div>
        )}
      </div>

      {/* Lightbox */}
      {showLightbox && enableLightbox && (
        <div 
          className="fixed inset-0 z-50 bg-black/80 flex items-center justify-center p-4"
          onClick={() => setShowLightbox(false)}
        >
          <div className="relative max-w-full max-h-full">
            <img
              src={src}
              alt={alt}
              className="max-w-full max-h-full object-contain"
              onClick={(e) => e.stopPropagation()}
            />
            
            {/* Close button */}
            <button
              onClick={() => setShowLightbox(false)}
              className="absolute top-4 right-4 p-2 bg-black/50 text-white rounded-full hover:bg-black/70 transition-colors"
            >
              <Icon name="x" size={20} />
            </button>

            {/* Download button in lightbox */}
            {showDownload && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleDownload();
                }}
                className="absolute top-4 right-16 p-2 bg-black/50 text-white rounded-full hover:bg-black/70 transition-colors"
              >
                <Icon name="download" size={20} />
              </button>
            )}
          </div>
        </div>
      )}
    </>
  );
};

export default ImageMessage;
