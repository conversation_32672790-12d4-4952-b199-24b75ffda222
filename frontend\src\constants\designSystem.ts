// frontend/src/constants/designSystem.ts

/**
 * Design System Constants
 * 
 * This file contains all design tokens and constants used throughout the application.
 * These constants ensure consistency across components and make it easy to maintain
 * the design system.
 */

// Typography
export const TYPOGRAPHY = {
  fontFamily: {
    primary: 'var(--font-family-primary)',
    secondary: 'var(--font-family-secondary)',
  },
  fontSize: {
    xs: '0.75rem',    // 12px
    sm: '0.875rem',   // 14px
    base: '1rem',     // 16px
    lg: '1.125rem',   // 18px
    xl: '1.25rem',    // 20px
    '2xl': '1.5rem',  // 24px
    '3xl': '1.875rem', // 30px
    '4xl': '2.25rem', // 36px
  },
  fontWeight: {
    normal: 'var(--font-weight-normal)',
    medium: 'var(--font-weight-medium)',
    semibold: 'var(--font-weight-semibold)',
    bold: 'var(--font-weight-bold)',
  },
  lineHeight: {
    tight: '1.25',
    base: 'var(--line-height-base)',
    relaxed: '1.75',
  },
} as const;

// Spacing Scale
export const SPACING = {
  xs: 'var(--spacing-xs)',     // 4px
  sm: 'var(--spacing-sm)',     // 8px
  md: 'var(--spacing-md)',     // 12px
  lg: 'var(--spacing-lg)',     // 16px
  xl: 'var(--spacing-xl)',     // 24px
  '2xl': 'var(--spacing-2xl)', // 32px
  '3xl': 'var(--spacing-3xl)', // 48px
} as const;

// Border Radius
export const BORDER_RADIUS = {
  sm: 'var(--radius-sm)',   // 8px - inputs
  md: 'var(--radius-md)',   // 12px - buttons
  lg: 'var(--radius-lg)',   // 16px - cards
  xl: 'var(--radius-xl)',   // 20px - modals
  full: 'var(--radius-full)', // 9999px - pills
} as const;

// Colors
export const COLORS = {
  primary: {
    DEFAULT: 'var(--color-primary)',
    hover: 'var(--color-primary-hover)',
    light: 'var(--color-primary-light)',
  },
  secondary: {
    DEFAULT: 'var(--color-secondary)',
    hover: 'var(--color-secondary-hover)',
  },
  danger: {
    DEFAULT: 'var(--color-danger)',
    hover: 'var(--color-danger-hover)',
  },
  success: 'var(--color-success)',
  warning: 'var(--color-warning)',
  
  // Background colors
  background: {
    primary: 'var(--bg-primary)',
    secondary: 'var(--bg-secondary)',
    tertiary: 'var(--bg-tertiary)',
    glass: 'var(--bg-glass)',
    glassLight: 'var(--bg-glass-light)',
    glassHeavy: 'var(--bg-glass-heavy)',
    overlay: 'var(--bg-overlay)',
  },
  
  // Text colors
  text: {
    primary: 'var(--text-primary)',
    secondary: 'var(--text-secondary)',
    tertiary: 'var(--text-tertiary)',
    inverse: 'var(--text-inverse)',
    muted: 'var(--text-muted)',
  },
  
  // Border colors
  border: {
    primary: 'var(--border-primary)',
    secondary: 'var(--border-secondary)',
    glass: 'var(--border-glass)',
    focus: 'var(--border-focus)',
  },
} as const;

// Glassmorphism Effects
export const GLASSMORPHISM = {
  blur: {
    sm: 'var(--glass-blur-sm)',   // 8px
    md: 'var(--glass-blur)',      // 12px
    lg: 'var(--glass-blur-lg)',   // 16px
  },
  opacity: {
    light: 'var(--glass-opacity-light)', // 0.1
    default: 'var(--glass-opacity)',     // 0.2
    heavy: 'var(--glass-opacity-heavy)', // 0.3
  },
} as const;

// Shadows
export const SHADOWS = {
  sm: 'var(--shadow-sm)',
  md: 'var(--shadow-md)',
  lg: 'var(--shadow-lg)',
  xl: 'var(--shadow-xl)',
  glass: 'var(--shadow-glass)',
} as const;

// Gradients
export const GRADIENTS = {
  primary: 'var(--gradient-primary)',
  secondary: 'var(--gradient-secondary)',
  glass: 'var(--gradient-glass)',
} as const;

// Component Sizes
export const COMPONENT_SIZES = {
  button: {
    small: {
      padding: '0.375rem 0.75rem',  // py-1.5 px-3
      fontSize: TYPOGRAPHY.fontSize.sm,
      height: '2rem',               // 32px
    },
    medium: {
      padding: '0.5rem 1rem',       // py-2 px-4
      fontSize: TYPOGRAPHY.fontSize.sm,
      height: '2.5rem',             // 40px
    },
    large: {
      padding: '0.75rem 1.5rem',    // py-3 px-6
      fontSize: TYPOGRAPHY.fontSize.base,
      height: '3rem',               // 48px
    },
  },
  input: {
    small: {
      padding: '0.375rem 0.75rem',  // py-1.5 px-3
      fontSize: TYPOGRAPHY.fontSize.sm,
      height: '2rem',               // 32px
    },
    medium: {
      padding: '0.5rem 0.75rem',    // py-2 px-3
      fontSize: TYPOGRAPHY.fontSize.sm,
      height: '2.5rem',             // 40px
    },
    large: {
      padding: '0.75rem 1rem',      // py-3 px-4
      fontSize: TYPOGRAPHY.fontSize.base,
      height: '3rem',               // 48px
    },
  },
  avatar: {
    small: '2rem',      // 32px
    medium: '3rem',     // 48px
    large: '4rem',      // 64px
    extraLarge: '6rem', // 96px
  },
} as const;

// Animation Durations
export const ANIMATION = {
  duration: {
    fast: '150ms',
    normal: '250ms',
    slow: '350ms',
  },
  easing: {
    ease: 'ease',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out',
  },
} as const;

// Z-Index Scale
export const Z_INDEX = {
  dropdown: 50,
  sticky: 60,
  fixed: 70,
  modal: 80,
  popover: 90,
  tooltip: 100,
} as const;

// Breakpoints (matching Tailwind defaults)
export const BREAKPOINTS = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
} as const;

// Status Colors
export const STATUS_COLORS = {
  online: '#10b981',    // green-500
  busy: '#ef4444',      // red-500
  away: '#f59e0b',      // amber-500
  offline: '#6b7280',   // gray-500
} as const;

// Message Types
export const MESSAGE_TYPES = {
  TEXT: 'text',
  IMAGE: 'image',
  AUDIO: 'audio',
  FILE: 'file',
  GROUP_UPDATE: 'group_update',
  CALLING: 'calling',
} as const;

// Theme Types
export const THEMES = {
  LIGHT: 'light',
  DARK: 'dark',
  SYSTEM: 'system',
} as const;

// Export all constants as a single object for convenience
export const DESIGN_SYSTEM = {
  TYPOGRAPHY,
  SPACING,
  BORDER_RADIUS,
  COLORS,
  GLASSMORPHISM,
  SHADOWS,
  GRADIENTS,
  COMPONENT_SIZES,
  ANIMATION,
  Z_INDEX,
  BREAKPOINTS,
  STATUS_COLORS,
  MESSAGE_TYPES,
  THEMES,
} as const;

export default DESIGN_SYSTEM;
