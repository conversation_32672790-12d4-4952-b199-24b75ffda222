# Generated by Django 5.2.4 on 2025-09-19 08:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='OTP',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email', models.EmailField(max_length=254)),
                ('code', models.CharField(max_length=6)),
                ('purpose', models.CharField(choices=[('signup', 'Signup'), ('login', 'Login'), ('password_reset', 'Password Reset')], max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('expires_at', models.DateTimeField()),
                ('is_used', models.BooleanField(default=False)),
            ],
            options={
                'db_table': 'users_otp',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='OTPRateLimit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email', models.EmailField(max_length=254, unique=True)),
                ('request_count', models.IntegerField(default=0)),
                ('last_request', models.DateTimeField(auto_now=True)),
                ('blocked_until', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'users_otp_rate_limit',
            },
        ),
        migrations.RemoveField(
            model_name='user',
            name='created_at',
        ),
        migrations.RemoveField(
            model_name='user',
            name='first_name',
        ),
        migrations.RemoveField(
            model_name='user',
            name='is_verified',
        ),
        migrations.RemoveField(
            model_name='user',
            name='last_name',
        ),
        migrations.RemoveField(
            model_name='user',
            name='updated_at',
        ),
        migrations.RemoveField(
            model_name='user',
            name='username',
        ),
        migrations.AddField(
            model_name='user',
            name='is_online',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='user',
            name='name',
            field=models.CharField(default='User', max_length=100),
        ),
        migrations.AlterModelTable(
            name='user',
            table='users_user',
        ),
    ]
