// frontend/src/components/ui/Icon.tsx
import React from 'react';
import {
  User,
  Users,
  MessageCircle,
  Send,
  Settings,
  LogOut,
  Eye,
  EyeOff,
  Search,
  Plus,
  X,
  Check,
  AlertCircle,
  AlertTriangle,
  Loader2,
  RefreshCw,
  WifiOff,
  Lock,
  Server,
  Phone,
  PhoneCall,
  PhoneOff,
  Video,
  VideoOff,
  CheckCheck,
  AlertCircleIcon,
  Mail,
  UserPlus,
  Sun,
  Moon,
  Clock,
  Pin,
  VolumeX,
  Archive,
  Edit,
  Trash,
  Copy,
  Reply,
  ArrowDown,
  File,
  MoreHorizontal,
  Bookmark,
  Smile,
  Paperclip,
  FileText,
  ChartArea
} from 'lucide-react';
import { Google } from '../Icons/Google';

// Icon component mapping for consistent usage
export const Icons = {
  user: User,
  users: Users,
  message: MessageCircle,
  'message-circle': MessageCircle,
  send: Send,
  settings: Settings,
  logout: LogOut,
  'log-out': LogOut,
  eye: Eye,
  eyeOff: EyeOff,
  search: Search,
  plus: Plus,
  close: X,
  x: X,
  check: Check,
  alert: Alert<PERSON>ircle,
  'alert-triangle': Al<PERSON><PERSON>riangle,
  spinner: Loader2,
  loader: Loader2,
  'refresh-cw': RefreshCw,
  refresh: RefreshCw,
  'wifi-off': WifiOff,
  lock: Lock,
  server: Server,
  phone: Phone,
  'phone-call': PhoneCall,
  'phone-off': PhoneOff,
  video: Video,
  'video-off': VideoOff,
  "check-check": CheckCheck,
  "alert-circle": AlertCircleIcon,
  mail: Mail,
  'user-plus': UserPlus,
  sun: Sun,
  moon: Moon,
  clock: Clock,
  pin: Pin,
  'volume-x': VolumeX,
  archive: Archive,
  edit: Edit,
  trash: Trash,
  copy: Copy,
  reply: Reply,
  'arrow-down': ArrowDown,
  file: File,
  'more-horizontal': MoreHorizontal,
  bookmark: Bookmark,
  smile: Smile,
  paperclip: Paperclip,
  'file-text': FileText,
  'chart-area': ChartArea,
  google: Google,
} as const;

export interface IconProps {
  name: keyof typeof Icons;
  size?: number;
  className?: string;
}

export const Icon: React.FC<IconProps> = ({ name, size = 20, className = '' }) => {
  const IconComponent = Icons[name];

  if (!IconComponent) {
    console.error(`Icon "${name}" not found. Available icons:`, Object.keys(Icons));
    // Return a fallback icon (AlertCircle) if the requested icon doesn't exist
    return <AlertCircle size={size} className={`${className} text-red-500`} />;
  }

  return <IconComponent size={size} className={className} />;
};
