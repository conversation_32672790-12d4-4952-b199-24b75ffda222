// frontend/src/components/ui/SearchBar.tsx
import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Icon } from './Icon';

/**
 * Search result item interface
 */
export interface SearchResult {
  id: string;
  type: 'user' | 'conversation' | 'message';
  title: string;
  subtitle?: string;
  avatar?: string;
  timestamp?: Date;
  highlight?: string;
}

/**
 * Props for the SearchBar component
 */
export interface SearchBarProps {
  /** Placeholder text for the search input */
  placeholder?: string;
  /** Search results to display */
  results?: SearchResult[];
  /** Whether the search is currently loading */
  loading?: boolean;
  /** Callback when search query changes */
  onSearch?: (query: string) => void;
  /** Callback when a search result is selected */
  onResultSelect?: (result: SearchResult) => void;
  /** Callback when search is cleared */
  onClear?: () => void;
  /** Whether to show recent searches */
  showRecentSearches?: boolean;
  /** Recent search queries */
  recentSearches?: string[];
  /** Callback when a recent search is selected */
  onRecentSearchSelect?: (query: string) => void;
  /** Custom class name */
  className?: string;
  /** Whether the search bar is focused */
  autoFocus?: boolean;
  /** Debounce delay in milliseconds */
  debounceMs?: number;
}

/**
 * Enhanced SearchBar component for universal search functionality
 * 
 * Features:
 * - Real-time search with debouncing
 * - Search results dropdown with different result types
 * - Recent searches support
 * - Keyboard navigation (arrow keys, enter, escape)
 * - Loading states and empty states
 * - Glassmorphism design integration
 * - Accessibility support
 * 
 * @example
 * ```tsx
 * <SearchBar
 *   placeholder="Search conversations, users, or messages..."
 *   results={searchResults}
 *   loading={isSearching}
 *   onSearch={handleSearch}
 *   onResultSelect={handleResultSelect}
 *   showRecentSearches
 *   recentSearches={recentQueries}
 * />
 * ```
 */
export const SearchBar: React.FC<SearchBarProps> = ({
  placeholder = 'Search...',
  results = [],
  loading = false,
  onSearch,
  onResultSelect,
  onClear,
  showRecentSearches = false,
  recentSearches = [],
  onRecentSearchSelect,
  className = '',
  autoFocus = false,
  debounceMs = 300,
}) => {
  const [query, setQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const inputRef = useRef<HTMLInputElement>(null);
  const debounceRef = useRef<NodeJS.Timeout | undefined>(undefined);

  // Create a stable debounced search function
  const debouncedSearch = useCallback((searchQuery: string) => {
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    debounceRef.current = setTimeout(() => {
      if (searchQuery.trim() && onSearch) {
        onSearch(searchQuery.trim());
      }
    }, debounceMs);
  }, [onSearch, debounceMs]);

  // Handle search with debouncing
  useEffect(() => {
    debouncedSearch(query);

    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, [query, debouncedSearch]);

  // Auto focus
  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newQuery = e.target.value;
    setQuery(newQuery);
    setSelectedIndex(-1);
    setIsOpen(newQuery.length > 0 || showRecentSearches);
  };

  // Handle input focus
  const handleInputFocus = () => {
    setIsOpen(query.length > 0 || showRecentSearches);
  };

  // Handle input blur
  const handleInputBlur = () => {
    // Delay closing to allow result clicks
    setTimeout(() => setIsOpen(false), 150);
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) return;

    const totalItems = query.length > 0 ? results.length : recentSearches.length;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => (prev < totalItems - 1 ? prev + 1 : prev));
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => (prev > 0 ? prev - 1 : -1));
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0) {
          if (query.length > 0 && results[selectedIndex]) {
            onResultSelect?.(results[selectedIndex]);
          } else if (query.length === 0 && recentSearches[selectedIndex]) {
            onRecentSearchSelect?.(recentSearches[selectedIndex]);
          }
        }
        setIsOpen(false);
        break;
      case 'Escape':
        setIsOpen(false);
        inputRef.current?.blur();
        break;
    }
  };

  // Handle clear
  const handleClear = () => {
    setQuery('');
    setSelectedIndex(-1);
    setIsOpen(showRecentSearches);
    onClear?.();
    inputRef.current?.focus();
  };


  const containerClasses = [
    'relative w-full',
    className,
  ].filter(Boolean).join(' ');

  const inputClasses = [
    'w-full glass border border-border-glass rounded-lg px-4 py-3 pl-10 pr-10',
    'text-text-primary placeholder-text-tertiary',
    'focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary',
    'transition-all duration-200',
  ].join(' ');

  return (
    <div className={containerClasses}>
      {/* Search Input */}
      <div className="relative">
        <Icon
          name="search"
          className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-tertiary"
          size={18}
        />
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          onBlur={handleInputBlur}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className={inputClasses}
        />
        {(query.length > 0 || loading) && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            {loading ? (
              <Icon name="loader" className="text-text-tertiary animate-spin" size={18} />
            ) : (
              <button
                onClick={handleClear}
                className="text-text-tertiary hover:text-text-secondary transition-colors"
              >
                <Icon name="x" size={18} />
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default SearchBar;
