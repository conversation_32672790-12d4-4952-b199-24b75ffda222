// frontend/src/components/ui/messages/MessageWrapper.tsx
import React, { useState } from 'react';
import { Icon } from '../Icon';
import { ProfilePhotoAvatar } from '../ProfilePhotoAvatar';
import { useThemeClasses } from '../../../hooks/useThemeClasses';

/**
 * Message status types
 */
export type MessageStatus = 'sending' | 'sent' | 'delivered' | 'read' | 'failed';

/**
 * Message reaction
 */
export interface MessageReaction {
  emoji: string;
  count: number;
  users: string[];
  hasReacted: boolean;
}

/**
 * Props for the MessageWrapper component
 */
export interface MessageWrapperProps {
  /** Message content */
  children: React.ReactNode;
  /** Message ID */
  messageId: string;
  /** Whether this message is from the current user */
  isOwn?: boolean;
  /** Sender information */
  sender?: {
    id: string;
    name: string;
    avatar?: string;
  };
  /** Message timestamp */
  timestamp: Date;
  /** Message status */
  status?: MessageStatus;
  /** Whether to show avatar */
  showAvatar?: boolean;
  /** Whether to show timestamp */
  showTimestamp?: boolean;
  /** Whether this message is part of a group */
  isGrouped?: boolean;
  /** Whether this is the first message in a group */
  isFirstInGroup?: boolean;
  /** Whether this is the last message in a group */
  isLastInGroup?: boolean;
  /** Message reactions */
  reactions?: MessageReaction[];
  /** Whether the message is selected */
  isSelected?: boolean;
  /** Whether the message is being edited */
  isEditing?: boolean;
  /** Custom class name */
  className?: string;
  /** Click handler */
  onClick?: () => void;
  /** Long press handler for mobile */
  onLongPress?: () => void;
  /** Reaction handler */
  onReaction?: (emoji: string) => void;
  /** Reply handler */
  onReply?: () => void;
  /** Edit handler */
  onEdit?: () => void;
  /** Delete handler */
  onDelete?: () => void;
  /** Copy handler */
  onCopy?: () => void;
}

/**
 * Enhanced MessageWrapper component for all message types
 * 
 * Features:
 * - Message grouping and spacing
 * - Avatar display for non-current users
 * - Timestamp and status indicators
 * - Message reactions
 * - Context menu with actions
 * - Selection states
 * - Glassmorphism design system integration
 * 
 * @example
 * ```tsx
 * <MessageWrapper
 *   messageId="msg-123"
 *   isOwn={false}
 *   sender={{ id: "user-1", name: "John Doe", avatar: "/avatar.jpg" }}
 *   timestamp={new Date()}
 *   status="read"
 *   reactions={[{ emoji: "👍", count: 2, users: ["user-2", "user-3"], hasReacted: false }]}
 *   onReaction={(emoji) => handleReaction(emoji)}
 * >
 *   <TextMessage content="Hello world!" />
 * </MessageWrapper>
 * ```
 */
export const MessageWrapper: React.FC<MessageWrapperProps> = ({
  children,
  messageId,
  isOwn = false,
  sender,
  timestamp,
  status = 'sent',
  showAvatar = true,
  showTimestamp = true,
  isGrouped = false,
  isFirstInGroup = true,
  isLastInGroup = true,
  reactions = [],
  isSelected = false,
  isEditing = false,
  className = '',
  onClick,
  onLongPress,
  onReaction,
  onReply,
  onEdit,
  onDelete,
  onCopy,
}) => {
  const [showActions, setShowActions] = useState(false);
  const { isDark } = useThemeClasses();

  // Format timestamp
  const formatTimestamp = (date: Date): string => {
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 24 * 7) {
      return date.toLocaleDateString([], { weekday: 'short', hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' });
    }
  };

  // Get status icon
  const getStatusIcon = (): string | null => {
    if (!isOwn) return null;
    
    switch (status) {
      case 'sending':
        return 'clock';
      case 'sent':
        return 'check';
      case 'delivered':
        return 'checkCheck';
      case 'read':
        return 'checkCheck';
      case 'failed':
        return 'alertCircle';
      default:
        return null;
    }
  };

  // Get status color
  const getStatusColor = (): string => {
    switch (status) {
      case 'sending':
        return 'text-text-tertiary';
      case 'sent':
        return 'text-text-tertiary';
      case 'delivered':
        return 'text-text-secondary';
      case 'read':
        return 'text-primary';
      case 'failed':
        return 'text-danger';
      default:
        return 'text-text-tertiary';
    }
  };

  const statusIcon = getStatusIcon();
  const statusColor = getStatusColor();

  // Container classes based on NewChatUI reference design
  const containerClasses = [
    'flex',
    isOwn ? 'justify-end' : 'justify-start',
    'mb-4', // Consistent spacing between messages
    className,
  ].filter(Boolean).join(' ');

  // Message wrapper classes for the NewChatUI layout
  const messageWrapperClasses = [
    'flex items-end space-x-2 max-w-xs lg:max-w-md',
  ].filter(Boolean).join(' ');

  return (
    <React.Fragment>
      <div
        className={containerClasses}
        onClick={onClick}
        onContextMenu={(e) => {
          e.preventDefault();
          setShowActions(!showActions);
        }}
      >
        <div className={messageWrapperClasses}>
          {/* Avatar for received messages (NewChatUI layout) */}
          {!isOwn && showAvatar && sender && (
            <ProfilePhotoAvatar
              src={sender.avatar}
              name={sender.name}
              size="small"
              className="w-8 h-8 rounded-full object-cover"
            />
          )}

          {/* Message bubble container */}
          <div className="relative">
            {/* Message bubble with theme-aware styling */}
            <div className={isEditing ? 'ring-2 ring-primary/50 rounded-lg' : ''}>
              {children}
            </div>

            {/* Timestamp and status indicators (NewChatUI style) */}
            {(showTimestamp || statusIcon) && (
              <div className="flex items-center justify-end mt-1 space-x-1">
                {showTimestamp && (
                  <span className={`text-xs ${isDark ? 'text-gray-300' : 'text-gray-500'}`}>
                    {formatTimestamp(timestamp)}
                  </span>
                )}

                {/* Status indicators for sent messages (double dots) */}
                {isOwn && statusIcon && (
                  <div className="flex space-x-1">
                    <div className={`w-1 h-1 rounded-full ${isDark ? 'bg-gray-300' : 'bg-gray-500'}`}></div>
                    <div className={`w-1 h-1 rounded-full ${isDark ? 'bg-gray-300' : 'bg-gray-500'}`}></div>
                  </div>
                )}
              </div>
            )}

            {/* Reactions (simplified for NewChatUI design) */}
            {reactions.length > 0 && (
              <div className="flex flex-wrap gap-1 mt-1">
                {reactions.map((reaction, index) => (
                  <button
                    key={index}
                    onClick={() => onReaction?.(reaction.emoji)}
                    className={`inline-flex items-center px-2 py-1 rounded-full text-xs transition-colors ${
                      reaction.hasReacted
                        ? 'bg-teal-500/20 text-teal-500 border border-teal-500/30'
                        : isDark
                          ? 'bg-gray-700 hover:bg-gray-600 border border-gray-600'
                          : 'bg-gray-200 hover:bg-gray-300 border border-gray-300'
                    }`}
                  >
                    <span className="mr-1">{reaction.emoji}</span>
                    <span>{reaction.count}</span>
                  </button>
                ))}
              </div>
            )}

            {/* Action menu */}
            {showActions && (
              <div className={`absolute top-0 z-10 glass border border-border-glass rounded-lg shadow-lg p-1 ${
                isOwn ? 'right-0' : 'left-0'
              }`}>
                <div className="flex space-x-1">
                  {onReaction && (
                    <button
                      onClick={() => onReaction('👍')}
                      className="p-1 rounded hover:bg-bg-glass-heavy transition-colors"
                      title="React"
                    >
                      <span className="text-sm">👍</span>
                    </button>
                  )}
                  
                  {onReply && (
                    <button
                      onClick={onReply}
                      className="p-1 rounded hover:bg-bg-glass-heavy transition-colors"
                      title="Reply"
                    >
                      <Icon name="reply" size={14} className="text-text-secondary" />
                    </button>
                  )}
                  
                  {onCopy && (
                    <button
                      onClick={onCopy}
                      className="p-1 rounded hover:bg-bg-glass-heavy transition-colors"
                      title="Copy"
                    >
                      <Icon name="copy" size={14} className="text-text-secondary" />
                    </button>
                  )}
                  
                  {isOwn && onEdit && (
                    <button
                      onClick={onEdit}
                      className="p-1 rounded hover:bg-bg-glass-heavy transition-colors"
                      title="Edit"
                    >
                      <Icon name="edit" size={14} className="text-text-secondary" />
                    </button>
                  )}
                  
                  {isOwn && onDelete && (
                    <button
                      onClick={onDelete}
                      className="p-1 rounded hover:bg-bg-glass-heavy transition-colors"
                      title="Delete"
                    >
                      <Icon name="trash" size={14} className="text-danger" />
                    </button>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Click outside to close actions */}
      {showActions && (
        <div 
          className="fixed inset-0 z-0" 
          onClick={() => setShowActions(false)}
        />
      )}
    </React.Fragment>
  );
};

export default MessageWrapper;
