// frontend/src/components/ui/Select.tsx
import React, { forwardRef, useState, useCallback, useRef, useEffect } from 'react';
import { useField, type FieldHookConfig } from 'formik';
import { Icon } from './Icon';

/**
 * Select component sizes
 */
export type SelectSize = 'small' | 'medium' | 'large';

/**
 * Option type for Select component
 */
export interface SelectOption {
  /** Unique value for the option */
  value: string | number;
  /** Display label for the option */
  label: string;
  /** Whether the option is disabled */
  disabled?: boolean;
  /** Optional icon for the option */
  icon?: string;
  /** Optional description for the option */
  description?: string;
}

/**
 * Base props for the Select component
 */
export interface BaseSelectProps {
  /** Label text for the select */
  label?: string;
  /** Error message to display */
  error?: string | boolean;
  /** Helper text to display below the select */
  helperText?: string;
  /** Size of the select */
  size?: SelectSize;
  /** Whether the select should take full width */
  fullWidth?: boolean;
  /** Placeholder text */
  placeholder?: string;
  /** Array of options */
  options: SelectOption[];
  /** Whether to enable search functionality */
  searchable?: boolean;
  /** Whether to allow multiple selections */
  multiple?: boolean;
  /** Whether the select is disabled */
  disabled?: boolean;
  /** Whether the select is required */
  required?: boolean;
  /** Custom option renderer */
  renderOption?: (option: SelectOption, isSelected: boolean) => React.ReactNode;
  /** Custom value renderer for selected items */
  renderValue?: (option: SelectOption | SelectOption[]) => React.ReactNode;
  /** Callback when selection changes */
  onChange?: (value: string | number | (string | number)[]) => void;
  /** Current value */
  value?: string | number | (string | number)[];
  /** Maximum number of selections (for multiple) */
  maxSelections?: number;
  /** Search placeholder text */
  searchPlaceholder?: string;
  /** No options found text */
  noOptionsText?: string;
}

/**
 * Props for Select component when used with Formik
 */
export interface FormikSelectProps extends BaseSelectProps {
  /** Formik field name */
  name: string;
}

/**
 * Props for Select component when used standalone
 */
export interface StandaloneSelectProps extends BaseSelectProps {
  name?: string;
}

export type SelectProps = FormikSelectProps | StandaloneSelectProps;

/**
 * Enhanced Select component with search functionality and Formik integration
 * 
 * Features:
 * - Seamless Formik integration with automatic error handling
 * - Search functionality with filtering
 * - Single and multi-select support
 * - Custom option and value rendering
 * - Keyboard navigation support
 * - Glassmorphism design system integration
 * - Comprehensive accessibility support
 * 
 * @example
 * ```tsx
 * // Single select with search
 * <Select 
 *   name="country" 
 *   label="Country" 
 *   options={countryOptions}
 *   searchable
 *   placeholder="Select a country..."
 * />
 * 
 * // Multi-select with custom rendering
 * <Select 
 *   name="skills" 
 *   label="Skills" 
 *   options={skillOptions}
 *   multiple
 *   maxSelections={5}
 *   renderOption={(option, isSelected) => (
 *     <div className="flex items-center">
 *       <Icon name={option.icon} className="mr-2" />
 *       {option.label}
 *     </div>
 *   )}
 * />
 * ```
 */
export const Select = forwardRef<HTMLDivElement, SelectProps>(({
  label,
  error: errorProp,
  helperText,
  size = 'medium',
  fullWidth = true,
  placeholder = 'Select an option...',
  options = [],
  searchable = false,
  multiple = false,
  disabled = false,
  required = false,
  renderOption,
  renderValue,
  onChange: onChangeProp,
  value: valueProp,
  maxSelections,
  searchPlaceholder = 'Search options...',
  noOptionsText = 'No options found',
  ...props
}, ref) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [focusedIndex, setFocusedIndex] = useState(-1);
  
  const selectRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const optionsRef = useRef<HTMLDivElement>(null);
  
  // Check if this is a Formik field
  const isFormikField = 'name' in props && props.name;
  
  // Use Formik field if name is provided
  const [field, meta] = isFormikField 
    ? useField(props as FieldHookConfig<string | string[]>)
    : [null, null];

  // Determine error state
  const hasError = errorProp || (meta?.touched && meta?.error);
  const errorMessage = typeof errorProp === 'string' ? errorProp : meta?.error;

  // Get current value
  const currentValue = field?.value ?? valueProp ?? (multiple ? [] : '');

  // Filter options based on search query
  const filteredOptions = searchable && searchQuery
    ? options.filter(option => 
        option.label.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : options;

  // Get selected options
  const selectedOptions = multiple
    ? options.filter(option => (currentValue as (string | number)[]).includes(option.value))
    : options.find(option => option.value === currentValue);

  // Handle option selection
  const handleOptionSelect = useCallback((option: SelectOption) => {
    if (option.disabled) return;

    let newValue: string | number | (string | number)[];

    if (multiple) {
      const currentArray = currentValue as (string | number)[];
      const isSelected = currentArray.includes(option.value);
      
      if (isSelected) {
        newValue = currentArray.filter(v => v !== option.value);
      } else {
        if (maxSelections && currentArray.length >= maxSelections) {
          return; // Don't add if max selections reached
        }
        newValue = [...currentArray, option.value];
      }
    } else {
      newValue = option.value;
      setIsOpen(false);
    }

    // Update value
    if (field?.onChange) {
      field.onChange({ target: { name: field.name, value: newValue } });
    } else if (onChangeProp) {
      onChangeProp(newValue);
    }

    setSearchQuery('');
    setFocusedIndex(-1);
  }, [currentValue, multiple, maxSelections, field, onChangeProp]);

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchQuery('');
        setFocusedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchable && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen, searchable]);

  // Size-specific classes
  const sizeClasses = {
    small: 'px-3 py-1.5 text-sm rounded-md min-h-[2rem]',
    medium: 'px-3 py-2 text-sm rounded-lg min-h-[2.5rem]',
    large: 'px-4 py-3 text-base rounded-xl min-h-[3rem]',
  };

  // Base select classes with glassmorphism
  const baseClasses = [
    'relative cursor-pointer transition-all duration-250',
    'glass-light border-border-glass',
    'focus:border-border-focus focus:ring-2 focus:ring-primary/20',
    'disabled:opacity-50 disabled:cursor-not-allowed',
    sizeClasses[size],
  ].join(' ');

  // Error state classes
  const errorClasses = hasError 
    ? 'border-danger focus:border-danger focus:ring-danger/20' 
    : '';

  const selectClasses = `${baseClasses} ${errorClasses}`;

  // Render selected value
  const renderSelectedValue = () => {
    if (multiple && Array.isArray(selectedOptions) && selectedOptions.length > 0) {
      if (renderValue) {
        return renderValue(selectedOptions);
      }
      return (
        <div className="flex flex-wrap gap-1">
          {selectedOptions.map(option => (
            <span
              key={option.value}
              className="inline-flex items-center px-2 py-1 rounded-md bg-primary/10 text-primary text-xs"
            >
              {option.label}
              <button
                type="button"
                onClick={(e) => {
                  e.stopPropagation();
                  handleOptionSelect(option);
                }}
                className="ml-1 hover:text-danger"
              >
                <Icon name="x" size={12} />
              </button>
            </span>
          ))}
        </div>
      );
    }

    if (!multiple && selectedOptions) {
      if (renderValue) {
        return renderValue(selectedOptions as SelectOption);
      }
      return (selectedOptions as SelectOption).label;
    }

    return <span className="text-text-tertiary">{placeholder}</span>;
  };

  return (
    <div className={`space-y-1 ${fullWidth ? 'w-full' : ''}`} ref={ref}>
      {/* Label */}
      {label && (
        <label className="block text-sm font-medium text-text-primary">
          {label}
          {required && <span className="text-danger ml-1">*</span>}
        </label>
      )}

      {/* Select container */}
      <div className="relative" ref={selectRef}>
        <div
          className={selectClasses}
          onClick={() => !disabled && setIsOpen(!isOpen)}
          role="combobox"
          aria-expanded={isOpen}
          aria-haspopup="listbox"
          tabIndex={disabled ? -1 : 0}
        >
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              {renderSelectedValue()}
            </div>
            <Icon 
              name={isOpen ? 'chevronUp' : 'chevronDown'} 
              className="ml-2 text-text-tertiary flex-shrink-0" 
              size={16} 
            />
          </div>
        </div>

        {/* Dropdown */}
        {isOpen && (
          <div className="absolute z-50 w-full mt-1 glass border-border-glass rounded-lg shadow-lg max-h-60 overflow-hidden">
            {/* Search input */}
            {searchable && (
              <div className="p-2 border-b border-border-glass">
                <div className="relative">
                  <Icon 
                    name="search" 
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-tertiary" 
                    size={14} 
                  />
                  <input
                    ref={searchInputRef}
                    type="text"
                    className="w-full pl-9 pr-3 py-1.5 text-sm bg-transparent border-none focus:outline-none text-text-primary placeholder-text-tertiary"
                    placeholder={searchPlaceholder}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>
            )}

            {/* Options */}
            <div className="max-h-48 overflow-y-auto" ref={optionsRef}>
              {filteredOptions.length === 0 ? (
                <div className="px-3 py-2 text-sm text-text-tertiary">
                  {noOptionsText}
                </div>
              ) : (
                filteredOptions.map((option, index) => {
                  const isSelected = multiple
                    ? (currentValue as (string | number)[]).includes(option.value)
                    : currentValue === option.value;

                  return (
                    <div
                      key={option.value}
                      className={`px-3 py-2 cursor-pointer transition-colors ${
                        option.disabled
                          ? 'opacity-50 cursor-not-allowed'
                          : 'hover:bg-bg-glass-heavy'
                      } ${isSelected ? 'bg-primary/10 text-primary' : 'text-text-primary'}`}
                      onClick={() => handleOptionSelect(option)}
                      role="option"
                      aria-selected={isSelected}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          {renderOption ? renderOption(option, isSelected) : (
                            <div>
                              <div className="text-sm">{option.label}</div>
                              {option.description && (
                                <div className="text-xs text-text-tertiary">
                                  {option.description}
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                        {isSelected && (
                          <Icon name="check" className="ml-2 text-primary" size={16} />
                        )}
                      </div>
                    </div>
                  );
                })
              )}
            </div>
          </div>
        )}
      </div>

      {/* Helper text and error message */}
      {(hasError && errorMessage) || helperText ? (
        <div>
          {hasError && errorMessage && (
            <p className="text-sm text-danger flex items-center">
              <Icon name="alert" className="mr-1 flex-shrink-0" size={14} />
              {errorMessage}
            </p>
          )}
          
          {!hasError && helperText && (
            <p className="text-sm text-text-secondary">
              {helperText}
            </p>
          )}
        </div>
      ) : null}
    </div>
  );
});

Select.displayName = 'Select';
