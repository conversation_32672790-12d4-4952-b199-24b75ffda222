// socket-server/src/services/conversationService.ts
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';
import { ConversationCreateSchema, ConversationCreate } from '../schemas';

export class ConversationService {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  async getUserConversations(userId: string) {
    try {
      const conversations = await this.prisma.conversation.findMany({
        where: {
          participants: {
            some: {
              userId: userId
            }
          }
        },
        include: {
          participants: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  profile_picture: true
                }
              }
            }
          },
          messages: {
            take: 1,
            orderBy: {
              createdAt: 'desc'
            },
            include: {
              sender: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  profile_picture: true
                }
              }
            }
          }
        },
        orderBy: {
          updatedAt: 'desc'
        }
      });

      return conversations.map(conv => ({
        ...conv,
        lastMessage: conv.messages[0] || null
      }));
    } catch (error) {
      console.error('Error fetching user conversations:', error);
      throw error;
    }
  }

  async createConversation(data: ConversationCreate, creatorId: string) {
    try {
      // Validate input
      const validatedData = ConversationCreateSchema.parse(data);

      // For direct messages, check if conversation already exists
      if (validatedData.type === 'DIRECT' && validatedData.participantIds.length === 1) {
        const otherUserId = validatedData.participantIds[0];
        const existingConversation = await this.findDirectConversation(creatorId, otherUserId);

        if (existingConversation) {
          return existingConversation;
        }
      }

      // Create new conversation
      const conversation = await this.prisma.conversation.create({
        data: {
          type: validatedData.type,
          name: validatedData.name || null,
          created_by_id: creatorId,
          description: '',
          group_settings: {},
          is_public: false,
          max_participants: validatedData.type === 'DIRECT' ? 2 : 100,
          createdAt: new Date()
        },
        include: {
          participants: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  profile_picture: true
                }
              }
            }
          }
        }
      });

      // Add creator as participant
      await this.prisma.conversationParticipant.create({
        data: {
          conversationId: conversation.id,
          userId: creatorId,
          role: 'ADMIN',
          can_add_members: true,
          can_delete_messages: true,
          can_edit_group_info: true,
          can_pin_messages: true,
          can_remove_members: true,
          is_active: true,
          mention_notifications_only: false,
          notifications_enabled: true,
          joinedAt: new Date()
        }
      });

      // Add other participants
      for (const participantId of validatedData.participantIds) {
        if (participantId !== creatorId) {
          await this.prisma.conversationParticipant.create({
            data: {
              conversationId: conversation.id,
              userId: participantId,
              role: 'MEMBER',
              can_add_members: false,
              can_delete_messages: false,
              can_edit_group_info: false,
              can_pin_messages: false,
              can_remove_members: false,
              is_active: true,
              mention_notifications_only: false,
              notifications_enabled: true,
              joinedAt: new Date()
            }
          });
        }
      }

      return conversation;
    } catch (error) {
      console.error('Error creating conversation:', error);
      throw error;
    }
  }

  private async findDirectConversation(userId1: string, userId2: string) {
    const conversation = await this.prisma.conversation.findFirst({
      where: {
        type: 'DIRECT',
        participants: {
          every: {
            userId: {
              in: [userId1, userId2]
            }
          }
        }
      },
      include: {
        participants: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                profile_picture: true
              }
            }
          }
        }
      }
    });

    return conversation;
  }

  async joinConversation(userId: string, conversationId: string) {
    try {
      const participant = await this.prisma.conversationParticipant.findFirst({
        where: {
          conversationId,
          userId
        }
      });

      return !!participant;
    } catch (error) {
      console.error('Error joining conversation:', error);
      return false;
    }
  }
}
