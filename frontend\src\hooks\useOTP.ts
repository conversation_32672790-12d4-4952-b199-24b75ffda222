import { useState, useCallback } from 'react';
import { 
  useRequestSignupOTPMutation, 
  useVerifySignupOTPMutation,
  useRequestLoginOTPMutation,
  useVerifyLoginOTPMutation 
} from '../services/authApi';
import type { OTPRequest, OTPVerifyRequest } from '../types';

export interface OTPState {
  isOTPSent: boolean;
  isLoading: boolean;
  error: string | null;
  countdown: number;
  canResend: boolean;
}

export interface UseOTPReturn {
  otpState: OTPState;
  requestOTP: (data: OTPRequest) => Promise<void>;
  verifyOTP: (data: OTPVerifyRequest) => Promise<{ success: boolean; data?: any }>;
  resetOTPState: () => void;
  startCountdown: () => void;
}

export const useOTP = (purpose: 'signup' | 'login'): UseOTPReturn => {
  const [otpState, setOTPState] = useState<OTPState>({
    isOTPSent: false,
    isLoading: false,
    error: null,
    countdown: 0,
    canResend: true,
  });

  // Select the appropriate mutations based on purpose
  const [requestSignupOTP] = useRequestSignupOTPMutation();
  const [verifySignupOTP] = useVerifySignupOTPMutation();
  const [requestLoginOTP] = useRequestLoginOTPMutation();
  const [verifyLoginOTP] = useVerifyLoginOTPMutation();

  const requestMutation = purpose === 'signup' ? requestSignupOTP : requestLoginOTP;
  const verifyMutation = purpose === 'signup' ? verifySignupOTP : verifyLoginOTP;

  const startCountdown = useCallback(() => {
    setOTPState(prev => ({ ...prev, countdown: 60, canResend: false }));
    
    const timer = setInterval(() => {
      setOTPState(prev => {
        if (prev.countdown <= 1) {
          clearInterval(timer);
          return { ...prev, countdown: 0, canResend: true };
        }
        return { ...prev, countdown: prev.countdown - 1 };
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const requestOTP = useCallback(async (data: OTPRequest) => {
    try {
      setOTPState(prev => ({ ...prev, isLoading: true, error: null }));
      
      const result = await requestMutation(data).unwrap();
      
      setOTPState(prev => ({ 
        ...prev, 
        isOTPSent: true, 
        isLoading: false,
        error: null 
      }));
      
      startCountdown();
    } catch (error: any) {
      setOTPState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error: error?.data?.message || error?.message || 'Failed to send OTP' 
      }));
      throw error;
    }
  }, [requestMutation, startCountdown]);

  const verifyOTP = useCallback(async (data: OTPVerifyRequest) => {
    try {
      setOTPState(prev => ({ ...prev, isLoading: true, error: null }));
      
      const result = await verifyMutation(data).unwrap();
      
      setOTPState(prev => ({ 
        ...prev, 
        isLoading: false,
        error: null 
      }));
      
      return { success: true, data: result };
    } catch (error: any) {
      setOTPState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error: error?.data?.message || error?.message || 'Invalid OTP' 
      }));
      return { success: false };
    }
  }, [verifyMutation]);

  const resetOTPState = useCallback(() => {
    setOTPState({
      isOTPSent: false,
      isLoading: false,
      error: null,
      countdown: 0,
      canResend: true,
    });
  }, []);

  return {
    otpState,
    requestOTP,
    verifyOTP,
    resetOTPState,
    startCountdown,
  };
};