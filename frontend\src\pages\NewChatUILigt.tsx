import {
    MessageCircle,
    Sun,
    MoreHorizontal,
    Search,
    User,
    Phone,
    Bookmark,
    Settings,
    Video,
    Smile,
    Paperclip,
    Send,
    X,
    FileText,
    ChartArea,
} from "lucide-react";

const NewChatUILight = () => {
    return (
        <div className="flex h-screen bg-gray-50 text-gray-900">
            <div className="w-96 bg-white border-r border-gray-200 flex flex-col">
                <div className="p-4 border-b border-gray-200">
                    <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center space-x-3">
                            <div className="w-10 h-10 bg-teal-500 rounded-full flex items-center justify-center">
                                <MessageCircle className="w-5 h-5 text-white" />
                            </div>
                            <div>
                                <h2 className="font-semibold text-gray-900">Bujange Bapak</h2>
                                <p className="text-sm text-gray-500">My Account</p>
                            </div>
                        </div>
                        <div className="flex items-center space-x-2">
                            <button className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100">
                                <Sun className="w-5 h-5 text-gray-600" />
                            </button>
                            <button className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100">
                                <MoreHorizontal className="w-5 h-5 text-gray-600" />
                            </button>
                        </div>
                    </div>
                    <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                        <input
                            placeholder="Search or start new chat..."
                            className="w-full bg-gray-100 text-gray-900 placeholder-gray-400 pl-10 pr-4 py-2 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-teal-500"
                            type="text"
                            value=""
                        />
                    </div>
                </div>
                <div className="flex-1 overflow-y-auto">
                    <div className="p-4">
                        <div className="flex items-center justify-between mb-4">
                            <h3 className="text-gray-900 font-medium">Messages</h3>
                            <span className="bg-teal-500 text-white text-xs px-2 py-1 rounded-full">
                                20
                            </span>
                        </div>
                        <div className="space-y-2">
                            <div className="flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-colors hover:bg-gray-50">
                                <div className="relative">
                                    <img
                                        alt="Putra Pratama"
                                        className="w-12 h-12 rounded-full object-cover"
                                        src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&amp;h=100&amp;fit=crop&amp;crop=face"
                                    />
                                    <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
                                </div>
                                <div className="flex-1 min-w-0">
                                    <div className="flex items-center justify-between">
                                        <h4 className="text-gray-900 font-medium truncate">
                                            Putra Pratama
                                        </h4>
                                        <span className="text-xs text-gray-500">13:10 PM</span>
                                    </div>
                                    <p className="text-sm text-gray-500 truncate">
                                        Oul, ws madang urung?
                                    </p>
                                </div>
                            </div>
                            <div className="flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-colors bg-gray-100">
                                <div className="relative">
                                    <img
                                        alt="Michael"
                                        className="w-12 h-12 rounded-full object-cover"
                                        src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&amp;h=100&amp;fit=crop&amp;crop=face"
                                    />
                                    <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
                                </div>
                                <div className="flex-1 min-w-0">
                                    <div className="flex items-center justify-between">
                                        <h4 className="text-gray-900 font-medium truncate">
                                            Michael
                                        </h4>
                                        <span className="text-xs text-gray-500">12:00 PM</span>
                                    </div>
                                    <p className="text-sm text-gray-500 truncate">Typing...</p>
                                </div>
                            </div>
                            <div className="flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-colors hover:bg-gray-50">
                                <div className="relative">
                                    <img
                                        alt="Jarwo"
                                        className="w-12 h-12 rounded-full object-cover"
                                        src="https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&amp;h=100&amp;fit=crop&amp;crop=face"
                                    />
                                    <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
                                </div>
                                <div className="flex-1 min-w-0">
                                    <div className="flex items-center justify-between">
                                        <h4 className="text-gray-900 font-medium truncate">
                                            Jarwo
                                        </h4>
                                        <span className="text-xs text-gray-500">08:00 AM</span>
                                    </div>
                                    <p className="text-sm text-gray-500 truncate">
                                        Jam 3 sore ana meeting
                                    </p>
                                </div>
                            </div>
                            <div className="flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-colors hover:bg-gray-50">
                                <div className="relative">
                                    <img
                                        alt="Kasmad"
                                        className="w-12 h-12 rounded-full object-cover"
                                        src="https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=100&amp;h=100&amp;fit=crop&amp;crop=face"
                                    />
                                    <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
                                </div>
                                <div className="flex-1 min-w-0">
                                    <div className="flex items-center justify-between">
                                        <h4 className="text-gray-900 font-medium truncate">
                                            Kasmad
                                        </h4>
                                        <span className="text-xs text-gray-500">10:32 AM</span>
                                    </div>
                                    <p className="text-sm text-gray-500 truncate">
                                        Ndeleng PR B.Inggris a lah...
                                    </p>
                                </div>
                                <div className="w-5 h-5 bg-teal-500 rounded-full flex items-center justify-center">
                                    <span className="text-xs text-white">3</span>
                                </div>
                            </div>
                            <div className="flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-colors hover:bg-gray-50">
                                <div className="relative">
                                    <img
                                        alt="Mawar"
                                        className="w-12 h-12 rounded-full object-cover"
                                        src="https://images.unsplash.com/photo-1494790108755-2616b612b1c5?w=100&amp;h=100&amp;fit=crop&amp;crop=face"
                                    />
                                </div>
                                <div className="flex-1 min-w-0">
                                    <div className="flex items-center justify-between">
                                        <h4 className="text-gray-900 font-medium truncate">
                                            Mawar
                                        </h4>
                                        <span className="text-xs text-gray-500">Yesterday</span>
                                    </div>
                                    <p className="text-sm text-gray-500 truncate">
                                        Siap, nanti saya kabari.
                                    </p>
                                </div>
                                <div className="w-5 h-5 bg-teal-500 rounded-full flex items-center justify-center">
                                    <span className="text-xs text-white">1</span>
                                </div>
                            </div>
                            <div className="flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-colors hover:bg-gray-50">
                                <div className="relative">
                                    <img
                                        alt="Melati"
                                        className="w-12 h-12 rounded-full object-cover"
                                        src="https://images.unsplash.com/photo-1517841905240-472988babdf9?w=100&amp;h=100&amp;fit=crop&amp;crop=face"
                                    />
                                </div>
                                <div className="flex-1 min-w-0">
                                    <div className="flex items-center justify-between">
                                        <h4 className="text-gray-900 font-medium truncate">
                                            Melati
                                        </h4>
                                        <span className="text-xs text-gray-500">Yesterday</span>
                                    </div>
                                    <p className="text-sm text-gray-500 truncate"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="p-4 border-t border-gray-200">
                    <div className="flex justify-around">
                        <button className="w-10 h-10 flex items-center justify-center text-gray-500 hover:text-gray-900">
                            <User className="w-5 h-5" />
                        </button>
                        <button className="w-10 h-10 flex items-center justify-center text-gray-500 hover:text-gray-900">
                            <Phone className="w-5 h-5" />
                        </button>
                        <button className="w-10 h-10 flex items-center justify-center text-gray-500 hover:text-gray-900">
                            <ChartArea className="w-5 h-5" />
                        </button>
                        <button className="w-10 h-10 flex items-center justify-center text-gray-500 hover:text-gray-900">
                            <Bookmark className="w-5 h-5" />
                        </button>
                        <button className="w-10 h-10 flex items-center justify-center text-gray-500 hover:text-gray-900">
                            <Settings className="w-5 h-5" />
                        </button>
                    </div>
                </div>
            </div>
            <div className="flex-1 flex">
                <div className="flex-1 flex flex-col bg-white">
                    <div className="p-4 border-b border-gray-200 flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                            <img
                                alt="Michael"
                                className="w-10 h-10 rounded-full object-cover"
                                src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&amp;h=100&amp;fit=crop&amp;crop=face"
                            />
                            <div>
                                <h3 className="text-gray-900 font-medium">Michael</h3>
                                <p className="text-sm text-gray-500">Typing...</p>
                            </div>
                        </div>
                        <div className="flex items-center space-x-2">
                            <button className="w-10 h-10 flex items-center justify-center text-gray-500 hover:text-gray-900 hover:bg-gray-100 rounded-full">
                                <Phone className="w-5 h-5" />
                            </button>
                            <button className="w-10 h-10 flex items-center justify-center text-gray-500 hover:text-gray-900 hover:bg-gray-100 rounded-full">
                                <Video className="w-5 h-5" />
                            </button>
                            <button className="w-10 h-10 flex items-center justify-center text-gray-500 hover:text-gray-900 hover:bg-gray-100 rounded-full">
                                <MoreHorizontal className="w-5 h-5" />
                            </button>
                        </div>
                    </div>
                    <div className="flex-1 overflow-y-auto p-4 space-y-4">
                        <div className="flex justify-center">
                            <span className="bg-gray-200 text-gray-600 text-sm px-3 py-1 rounded-full">
                                Today
                            </span>
                        </div>
                        <div className="flex justify-start">
                            <div className="flex items-end space-x-2 max-w-xs lg:max-w-md">
                                <img
                                    alt="Michael"
                                    className="w-8 h-8 rounded-full object-cover"
                                    src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&amp;h=100&amp;fit=crop&amp;crop=face"
                                />
                                <div className="relative px-4 py-2 rounded-2xl bg-gray-200 text-gray-900 rounded-bl-none">
                                    <p className="text-sm">Hello, Nizar Ali</p>
                                    <div className="flex items-center justify-end mt-1 space-x-1">
                                        <span className="text-xs text-gray-500">12:00 PM</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="flex justify-start">
                            <div className="flex items-end space-x-2 max-w-xs lg:max-w-md">
                                <img
                                    alt="Michael"
                                    className="w-8 h-8 rounded-full object-cover"
                                    src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&amp;h=100&amp;fit=crop&amp;crop=face"
                                />
                                <div className="relative px-4 py-2 rounded-2xl bg-gray-200 text-gray-900 rounded-bl-none">
                                    <p className="text-sm">
                                        Good afternoon, may i ask your help to make me a real estate
                                        landing page, for more details i will send as soon as you
                                        approve it. thanks!
                                    </p>
                                    <div className="flex items-center justify-end mt-1 space-x-1">
                                        <span className="text-xs text-gray-500">12:01 PM</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="flex justify-end">
                            <div className="flex items-end space-x-2 max-w-xs lg:max-w-md">
                                <div className="relative px-4 py-2 rounded-2xl bg-teal-500 text-white rounded-br-none">
                                    <p className="text-sm">Haii, Michael!</p>
                                    <div className="flex items-center justify-end mt-1 space-x-1">
                                        <span className="text-xs text-gray-300">12:10 PM</span>
                                        <div className="flex space-x-1">
                                            <div className="w-1 h-1 bg-gray-300 rounded-full"></div>
                                            <div className="w-1 h-1 bg-gray-300 rounded-full"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="flex justify-end">
                            <div className="flex items-end space-x-2 max-w-xs lg:max-w-md">
                                <div className="relative px-4 py-2 rounded-2xl bg-teal-500 text-white rounded-br-none">
                                    <p className="text-sm">
                                        Thank you for the offer you gave me, yes I will accept the
                                        project from you, for the brief please send it now so I can
                                        study it first.
                                    </p>
                                    <div className="flex items-center justify-end mt-1 space-x-1">
                                        <span className="text-xs text-gray-300">12:10 PM</span>
                                        <div className="flex space-x-1">
                                            <div className="w-1 h-1 bg-gray-300 rounded-full"></div>
                                            <div className="w-1 h-1 bg-gray-300 rounded-full"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="flex justify-start">
                            <div className="flex items-end space-x-2 max-w-xs lg:max-w-md">
                                <img
                                    alt="Michael"
                                    className="w-8 h-8 rounded-full object-cover"
                                    src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&amp;h=100&amp;fit=crop&amp;crop=face"
                                />
                                <div className="relative px-4 py-2 rounded-2xl bg-gray-200 text-gray-900 rounded-bl-none">
                                    <div className="flex items-center space-x-3">
                                        <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                                            <FileText className="w-5 h-5 text-white" />
                                        </div>
                                        <div>
                                            <p className="font-medium text-sm">
                                                Brief Project Real Estate
                                            </p>
                                            <p className="text-xs text-gray-500">DOCX</p>
                                        </div>
                                        <p className="text-xs text-gray-500">32 kB</p>
                                    </div>
                                    <div className="flex items-center justify-end mt-1 space-x-1">
                                        <span className="text-xs text-gray-500">12:28 PM</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="flex justify-end">
                            <div className="flex items-end space-x-2 max-w-xs lg:max-w-md">
                                <div className="relative px-4 py-2 rounded-2xl bg-teal-500 text-white rounded-br-none">
                                    <p className="text-sm">
                                        Ohh i see, for payment we can directly discuss now
                                    </p>
                                    <div className="flex items-center justify-end mt-1 space-x-1">
                                        <span className="text-xs text-gray-300">12:30 PM</span>
                                        <div className="flex space-x-1">
                                            <div className="w-1 h-1 bg-gray-300 rounded-full"></div>
                                            <div className="w-1 h-1 bg-gray-300 rounded-full"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="p-4 border-t border-gray-200">
                        <div className="flex items-center space-x-3">
                            <button className="w-10 h-10 flex items-center justify-center text-gray-500 hover:text-gray-900 hover:bg-gray-100 rounded-full">
                                <Smile className="w-5 h-5" />
                            </button>
                            <div className="flex-1 relative">
                                <input
                                    placeholder="Message to Tedy..."
                                    className="w-full bg-gray-100 text-gray-900 placeholder-gray-500 px-4 py-3 rounded-full focus:outline-none focus:ring-2 focus:ring-teal-500"
                                    type="text"
                                    value=""
                                />
                                <button className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-900">
                                    <Paperclip className="w-5 h-5" />
                                </button>
                            </div>
                            <button className="w-10 h-10 flex items-center justify-center bg-teal-500 text-white rounded-full hover:bg-teal-600 transition-colors">
                                <Send className="w-5 h-5" />
                            </button>
                        </div>
                    </div>
                </div>
                <div className="w-80 bg-white border-l border-gray-200 overflow-y-auto">
                    <div className="p-4 border-b border-gray-200 flex items-center justify-between">
                        <h3 className="text-gray-900 font-medium">Contact detail</h3>
                        <button className="w-8 h-8 flex items-center justify-center text-gray-500 hover:text-gray-900 hover:bg-gray-100 rounded-full">
                            <X className="w-5 h-5" />
                        </button>
                    </div>
                    <div className="p-6 text-center border-b border-gray-200">
                        <div className="relative inline-block mb-4">
                            <img
                                alt="Michael"
                                className="w-20 h-20 rounded-full object-cover mx-auto"
                                src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&amp;h=100&amp;fit=crop&amp;crop=face"
                            />
                            <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-white"></div>
                        </div>
                        <h4 className="text-gray-900 font-semibold text-lg">Michael</h4>
                        <p className="text-teal-400 text-sm">Online</p>
                    </div>
                    <div className="p-4 border-b border-gray-200">
                        <div className="flex items-center justify-between mb-4">
                            <h5 className="text-gray-900 font-medium">Media</h5>
                            <div className="flex items-center space-x-2">
                                <span className="text-gray-500 text-sm">12 pictures</span>
                                <button className="text-teal-400 text-sm hover:text-teal-300 whitespace-nowrap">
                                    View all
                                </button>
                            </div>
                        </div>
                        <div className="grid grid-cols-3 gap-2">
                            <div className="aspect-square">
                                <img
                                    alt="Media 1"
                                    className="w-full h-full object-cover rounded-lg"
                                    src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=200&amp;h=200&amp;fit=crop"
                                />
                            </div>
                            <div className="aspect-square">
                                <img
                                    alt="Media 2"
                                    className="w-full h-full object-cover rounded-lg"
                                    src="https://images.unsplash.com/photo-1551650975-87deedd944c3?w=200&amp;h=200&amp;fit=crop"
                                />
                            </div>
                            <div className="aspect-square">
                                <img
                                    alt="Media 3"
                                    className="w-full h-full object-cover rounded-lg"
                                    src="https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=200&amp;h=200&amp;fit=crop"
                                />
                            </div>
                            <div className="aspect-square">
                                <img
                                    alt="Media 4"
                                    className="w-full h-full object-cover rounded-lg"
                                    src="https://images.unsplash.com/photo-1560472355-536de3962603?w=200&amp;h=200&amp;fit=crop"
                                />
                            </div>
                            <div className="aspect-square">
                                <img
                                    alt="Media 5"
                                    className="w-full h-full object-cover rounded-lg"
                                    src="https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=200&amp;h=200&amp;fit=crop"
                                />
                            </div>
                            <div className="aspect-square">
                                <img
                                    alt="Media 6"
                                    className="w-full h-full object-cover rounded-lg"
                                    src="https://images.unsplash.com/photo-1512917774080-9991f1c4c750?w=200&amp;h=200&amp;fit=crop"
                                />
                            </div>
                            <div className="aspect-square">
                                <img
                                    alt="Media 7"
                                    className="w-full h-full object-cover rounded-lg"
                                    src="https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=200&amp;h=200&amp;fit=crop"
                                />
                            </div>
                            <div className="aspect-square">
                                <img
                                    alt="Media 8"
                                    className="w-full h-full object-cover rounded-lg"
                                    src="https://images.unsplash.com/photo-1580587771525-78b9dba3b914?w=200&amp;h=200&amp;fit=crop"
                                />
                            </div>
                            <div className="aspect-square bg-gray-200 rounded-lg flex items-center justify-center">
                                <span className="text-gray-900 font-semibold">+11</span>
                            </div>
                        </div>
                    </div>
                    <div className="p-4">
                        <div className="flex items-center justify-between mb-4">
                            <h5 className="text-gray-900 font-medium">Files</h5>
                            <div className="flex items-center space-x-2">
                                <span className="text-gray-500 text-sm">12 Files</span>
                                <button className="text-teal-400 text-sm hover:text-teal-300 whitespace-nowrap">
                                    View all
                                </button>
                            </div>
                        </div>
                        <div className="space-y-3">
                            <div className="flex items-center space-x-3">
                                <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                                    <FileText className="w-5 h-5 text-white" />
                                </div>
                                <div className="flex-1 min-w-0">
                                    <p className="text-gray-900 text-sm font-medium truncate">
                                        Brief Project Real Estate
                                    </p>
                                    <div className="flex items-center space-x-2 text-xs text-gray-500">
                                        <span>DOCX</span>
                                        <span>32 kB</span>
                                    </div>
                                </div>
                                <span className="text-xs text-gray-500 whitespace-nowrap">
                                    Today 12:28 PM
                                </span>
                            </div>
                            <div className="flex items-center space-x-3">
                                <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                                    <FileText className="w-5 h-5 text-white" />
                                </div>
                                <div className="flex-1 min-w-0">
                                    <p className="text-gray-900 text-sm font-medium truncate">
                                        Total project cost
                                    </p>
                                    <div className="flex items-center space-x-2 text-xs text-gray-500">
                                        <span>DOCX</span>
                                        <span>32 kB</span>
                                    </div>
                                </div>
                                <span className="text-xs text-gray-500 whitespace-nowrap">
                                    Yesterday
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default NewChatUILight;
