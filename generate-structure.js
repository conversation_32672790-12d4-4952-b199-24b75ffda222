import fs from "fs";
import path from "path";

// Input/Output files
const INPUT_FILE = "deps.json"; // your uploaded Madge JSON
const OUTPUT_FILE = "component-structure-with-tsx-only.json";

// Load Madge dependency map
const deps = JSON.parse(fs.readFileSync(INPUT_FILE, "utf8"));

// Filter to include only .tsx files
function isTsx(filePath) {
  return filePath.endsWith(".tsx");
}

// Build a tree from flat keys
function buildTree(paths) {
  const root = {};
  for (const p of paths) {
    // Skip non-tsx files
    if (!isTsx(p)) continue;

    const parts = p.split("/");
    let current = root;
    for (const part of parts) {
      if (!current[part]) {
        current[part] = { __children: {} };
      }
      current = current[part].__children;
    }
  }
  return root;
}

// Convert tree object to structured JSON
function formatTree(node, prefix = "") {
  return Object.entries(node)
    .map(([name, value]) => {
      const fullPath = path.posix.join(prefix, name);

      // Filter children to include only tsx files
      const children = formatTree(value.__children, fullPath).filter(
        (child) => child.type === "folder" || isTsx(child.path)
      );

      return {
        name,
        type: children.length ? "folder" : "file",
        path: fullPath,
        calls: deps[fullPath]?.filter(isTsx) || [],
        children,
      };
    })
    .filter((item) => item.type === "folder" || isTsx(item.path));
}

// All unique tsx file paths from deps keys + values
const allFiles = new Set(
  [
    ...Object.keys(deps),
    ...Object.values(deps).flat(),
  ].filter(isTsx)
);

// Build folder tree
const tree = buildTree([...allFiles]);
const structured = formatTree(tree);

// Save result
fs.writeFileSync(OUTPUT_FILE, JSON.stringify(structured, null, 2));
console.log(`✅ Component structure with only TSX files written to ${OUTPUT_FILE}`);
