{"App.tsx": ["store/index.ts"], "components/Chat/ChatHeader.tsx": ["store/index.ts", "store/slices/callingSlice.ts", "store/slices/conversationSlice.ts"], "components/calling/ActiveCallInterface.tsx": ["store/slices/callingSlice.ts"], "components/calling/CallControls.tsx": ["store/index.ts", "store/slices/callingSlice.ts"], "components/calling/CallManager.tsx": ["store/index.ts", "store/slices/callingSlice.ts"], "components/calling/CallStatusIndicator.tsx": ["store/slices/callingSlice.ts"], "components/calling/IncomingCallModal.tsx": ["store/slices/callingSlice.ts"], "components/calling/VideoContainer.tsx": ["store/slices/callingSlice.ts"], "components/ui/MessageInput.tsx": ["store/index.ts"], "containers/MessageContainer.tsx": ["store/index.ts", "store/slices/messageSlice.ts"], "contexts/EncryptionContext.tsx": ["store/index.ts"], "contexts/SocketContext.tsx": ["store/index.ts", "store/slices/callingSlice.ts", "store/slices/conversationSlice.ts", "store/slices/messageSlice.ts"], "hooks/useMediaEncryption.ts": ["store/index.ts"], "hooks/useMessageDecryption.ts": ["store/slices/messageSlice.ts"], "hooks/useSocketCacheSync.ts": ["store/index.ts"], "pages/Dashboard.tsx": ["store/index.ts", "store/slices/conversationSlice.ts"], "services/messageApi.ts": ["store/slices/messageSlice.ts"], "services/webrtc/MediaService.ts": ["store/index.ts", "store/slices/callingSlice.ts"], "services/webrtc/SignalingService.ts": ["store/index.ts"], "services/webrtc/WebRTCService.ts": ["store/index.ts", "store/slices/callingSlice.ts"], "store/index.ts": ["store/slices/callingSlice.ts", "store/slices/conversationSlice.ts", "store/slices/encryptionSlice.ts", "store/slices/mediaUploadSlice.ts", "store/slices/messageSlice.ts"], "store/slices/callingSlice.ts": ["store/index.ts"]}