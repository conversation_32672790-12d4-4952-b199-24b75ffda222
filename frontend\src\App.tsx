// frontend/src/App.tsx
import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Provider } from 'react-redux';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { EncryptionProvider } from './contexts/EncryptionContext';
import { SocketProvider } from './contexts/SocketContext';
import { ThemeProvider } from './contexts/ThemeContext';
import { ErrorBoundary } from './components/ui/ErrorBoundary';
import { useSocketCacheSync } from './hooks/useSocketCacheSync';
import { store } from './store';
import Login from './pages/Login';
import Register from './pages/Register';
import { Dashboard } from './pages/Dashboard';
import { CallManager } from './components/calling';
import NewChatUI from './pages/NewChatUI';

// Protected Route component
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return isAuthenticated ? <>{children}</> : <Navigate to="/login" />;
};

// Public Route component (redirect to dashboard if authenticated)
const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return !isAuthenticated ? <>{children}</> : <Navigate to="/dashboard" />;
};

// Component to initialize socket cache sync
const SocketCacheSync: React.FC = () => {
  useSocketCacheSync();
  return null;
};

const AppRoutes: React.FC = () => {
  return (
    <>
      <SocketCacheSync />
      <CallManager />
      <Routes>
        <Route path="/" element={<Navigate to="/dashboard" />} />
        <Route path="/new" element={<NewChatUI />} />
        <Route
          path="/login"
          element={
            <PublicRoute>
              <Login />
            </PublicRoute>
          }
        />
        <Route
          path="/register"
          element={
            <PublicRoute>
              <Register />
            </PublicRoute>
          }
        />
        <Route
          path="/dashboard"
          element={
            <ProtectedRoute>
              <Dashboard />
            </ProtectedRoute>
          }
        />

      </Routes>
    </>
  );
};

function App() {
  return (
    <ErrorBoundary>
      <Provider store={store}>
        <Router>
          <AuthProvider>
            <EncryptionProvider>
              <SocketProvider>
                <ThemeProvider>
                  <div className="App">
                    <AppRoutes />
                  </div>
                </ThemeProvider>
              </SocketProvider>
            </EncryptionProvider>
          </AuthProvider>
        </Router>
      </Provider>
    </ErrorBoundary>
  );
}

export default App;
