// frontend/src/components/ui/ProfilePhotoAvatar.tsx
import React, { useState, useCallback } from 'react';
import { Icon, type Icons } from './Icon';
import { STATUS_COLORS } from '../../constants/designSystem';

/**
 * Avatar component sizes
 */
export type AvatarSize = 'small' | 'medium' | 'large' | 'extraLarge';

/**
 * User status types
 */
export type UserStatus = 'online' | 'busy' | 'away' | 'offline';

/**
 * Props for the ProfilePhotoAvatar component
 */
export interface ProfilePhotoAvatarProps {
  /** User's profile photo URL */
  src?: string;
  /** User's name for generating initials */
  name: string;
  /** User's online status */
  status?: UserStatus;
  /** Size of the avatar */
  size?: AvatarSize;
  /** Whether to show status indicator */
  showStatus?: boolean;
  /** Custom status indicator position */
  statusPosition?: 'bottom-right' | 'top-right' | 'bottom-left' | 'top-left';
  /** Whether the avatar is clickable */
  clickable?: boolean;
  /** Click handler */
  onClick?: () => void;
  /** Custom class name */
  className?: string;
  /** Alt text for the image */
  alt?: string;
  /** Whether to show a border */
  showBorder?: boolean;
  /** Custom border color */
  borderColor?: string;
  /** Whether this is a group avatar */
  isGroup?: boolean;
  /** Array of user names for group avatar */
  groupMembers?: string[];
  /** Maximum number of initials to show in group avatar */
  maxGroupInitials?: number;
  /** Loading state */
  loading?: boolean;
  /** Custom placeholder icon */
  placeholderIcon?: keyof typeof Icons;
  /*** if photo is not available then title */
  title?: string;
}

/**
 * Enhanced ProfilePhotoAvatar component with status indicators and group support
 * 
 * Features:
 * - Profile photo display with fallback to initials
 * - Online status indicators with customizable positions
 * - Multiple sizes (small: 32px, medium: 48px, large: 64px, extraLarge: 96px)
 * - Group avatar support with multiple initials
 * - Loading states and error handling
 * - Glassmorphism design system integration
 * - Accessibility support
 * - Click handling for interactive avatars
 * 
 * @example
 * ```tsx
 * // Single user avatar with status
 * <ProfilePhotoAvatar 
 *   src="/path/to/photo.jpg"
 *   name="John Doe"
 *   status="online"
 *   size="medium"
 *   showStatus
 *   clickable
 *   onClick={() => openProfile()}
 * />
 * 
 * // Group avatar
 * <ProfilePhotoAvatar 
 *   name="Project Team"
 *   isGroup
 *   groupMembers={["John Doe", "Jane Smith", "Bob Wilson"]}
 *   size="large"
 * />
 * ```
 */
export const ProfilePhotoAvatar: React.FC<ProfilePhotoAvatarProps> = ({
  src,
  name,
  status = 'offline',
  size = 'medium',
  showStatus = false,
  statusPosition = 'bottom-right',
  clickable = false,
  onClick,
  className = '',
  alt,
  showBorder = false,
  borderColor,
  isGroup = false,
  groupMembers = [],
  maxGroupInitials = 3,
  loading = false,
  placeholderIcon = 'user',
}) => {
  const [imageError, setImageError] = useState(false);
  const [imageLoading, setImageLoading] = useState(true);

  // Handle image load error
  const handleImageError = useCallback(() => {
    setImageError(true);
    setImageLoading(false);
  }, []);

  const handleImageLoad = useCallback(() => {
    setImageLoading(false);
  }, []);

  // Generate initials from name
  const generateInitials = useCallback((fullName: string, maxInitials: number = 2): string => {
    if (!fullName) return '?';

    const words = fullName.trim().split(' ').filter(word => word.length > 0);
    const initials = words
      .slice(0, maxInitials)
      .map(word => word.charAt(0).toUpperCase())
      .join('');

    return initials || '?';
  }, []);

  // Generate group initials
  const generateGroupInitials = useCallback((): string => {
    if (groupMembers.length === 0) return generateInitials(name);

    const initials = groupMembers
      .slice(0, maxGroupInitials)
      .map(member => generateInitials(member, 1))
      .join('');

    return initials;
  }, [groupMembers, maxGroupInitials, name, generateInitials]);

  // Size configurations
  const sizeConfig = {
    small: {
      container: 'w-8 h-8',
      text: 'text-xs',
      statusSize: 'w-2.5 h-2.5',
      statusOffset: '-translate-x-0.5 -translate-y-0.5',
    },
    medium: {
      container: 'w-12 h-12',
      text: 'text-sm',
      statusSize: 'w-3 h-3',
      statusOffset: '-translate-x-1 -translate-y-1',
    },
    large: {
      container: 'w-16 h-16',
      text: 'text-lg',
      statusSize: 'w-4 h-4',
      statusOffset: '-translate-x-1 -translate-y-1',
    },
    extraLarge: {
      container: 'w-24 h-24',
      text: 'text-xl',
      statusSize: 'w-5 h-5',
      statusOffset: '-translate-x-1.5 -translate-y-1.5',
    },
  };

  // Status position classes
  const statusPositionClasses = {
    'bottom-right': 'bottom-0 right-0',
    'top-right': 'top-0 right-0',
    'bottom-left': 'bottom-0 left-0',
    'top-left': 'top-0 left-0',
  };

  // Status colors
  const statusColorClasses = {
    online: 'bg-green-500',
    busy: 'bg-red-500',
    away: 'bg-yellow-500',
    offline: 'bg-gray-400',
  };

  const config = sizeConfig[size];
  const initials = isGroup ? generateGroupInitials() : generateInitials(name);
  const shouldShowImage = src && !imageError && !loading;
  const isLoading = loading || (src && imageLoading);

  // Container classes
  const containerClasses = [
    'relative inline-flex items-center justify-center',
    'rounded-full overflow-hidden',
    'bg-gradient-to-br from-blue-500 to-purple-600',
    'text-white font-medium',
    config.container,
    config.text,
    clickable ? 'cursor-pointer hover:scale-105 transition-transform duration-200' : '',
    showBorder ? `ring-2 ring-offset-2 ${borderColor || 'ring-border-primary ring-offset-bg-primary'}` : '',
    className,
  ].filter(Boolean).join(' ');

  return (
    <div className={containerClasses} onClick={clickable ? onClick : undefined}>
      {/* Loading state */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-bg-glass">
          <Icon name="spinner" className="animate-spin text-text-tertiary" size={16} />
        </div>
      )}

      {/* Profile image */}
      {shouldShowImage && (
        <img
          src={src}
          alt={alt || `${name}'s profile photo`}
          className="w-full h-full object-cover"
          onError={handleImageError}
          onLoad={handleImageLoad}
        />
      )}

      {/* Initials fallback */}
      {!shouldShowImage && !isLoading && (
        <span className="select-none">
          {initials}
        </span>
      )}

      {/* Placeholder icon for empty state */}
      {!shouldShowImage && !isLoading && !name && (
        <Icon name={placeholderIcon} className="text-white/80" size={config.container === 'w-8 h-8' ? 16 : 24} />
      )}

      {/* Status indicator */}
      {showStatus && !isGroup && (
        <div
          className={`absolute ${statusPositionClasses[statusPosition]} ${config.statusOffset}`}
        >
          <div
            className={`${config.statusSize} rounded-full border-2 border-bg-primary ${statusColorClasses[status]}`}
            title={`Status: ${status}`}
          />
        </div>
      )}

      {/* Group indicator */}
      {isGroup && (
        <div className="absolute -bottom-1 -right-1">
          <div className="w-4 h-4 bg-bg-primary rounded-full flex items-center justify-center border border-border-primary">
            <Icon name="users" className="text-text-secondary" size={10} />
          </div>
        </div>
      )}
    </div>
  );
};

/**
 * Avatar group component for displaying multiple avatars
 */
export interface AvatarGroupProps {
  /** Array of avatar props */
  avatars: Omit<ProfilePhotoAvatarProps, 'size'>[];
  /** Size for all avatars */
  size?: AvatarSize;
  /** Maximum number of avatars to show */
  max?: number;
  /** Custom class name */
  className?: string;
  /** Click handler for overflow indicator */
  onOverflowClick?: () => void;
}

export const AvatarGroup: React.FC<AvatarGroupProps> = ({
  avatars,
  size = 'medium',
  max = 5,
  className = '',
  onOverflowClick,
}) => {
  const visibleAvatars = avatars.slice(0, max);
  const overflowCount = Math.max(0, avatars.length - max);

  const sizeClasses = {
    small: '-ml-1',
    medium: '-ml-2',
    large: '-ml-3',
    extraLarge: '-ml-4',
  };

  return (
    <div className={`flex items-center ${className}`}>
      {visibleAvatars.map((avatar, index) => (
        <div
          key={index}
          className={`${index > 0 ? sizeClasses[size] : ''} relative z-${10 - index}`}
        >
          <ProfilePhotoAvatar
            {...avatar}
            size={size}
            showBorder
            borderColor="ring-bg-primary"
          />
        </div>
      ))}

      {overflowCount > 0 && (
        <div
          className={`${sizeClasses[size]} relative z-0 ${onOverflowClick ? 'cursor-pointer' : ''}`}
          onClick={onOverflowClick}
        >
          <ProfilePhotoAvatar
            name={`+${overflowCount}`}
            size={size}
            showBorder
            borderColor="ring-bg-primary"
            className="bg-bg-tertiary text-text-secondary"
          />
        </div>
      )}
    </div>
  );
};

export default ProfilePhotoAvatar;
