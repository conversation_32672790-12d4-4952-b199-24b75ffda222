// frontend/src/components/ui/Form.tsx
import React, { type ReactNode } from 'react';
import { Formik, Form as FormikForm, type FormikConfig, type FormikValues } from 'formik';
import { Button, type ButtonProps } from './Button';
import { Icon } from './Icon';

/**
 * Form action button configuration
 */
export interface FormAction extends Omit<ButtonProps, 'type' | 'loading'> {
  /** Button label */
  label: string;
  /** Button type */
  type?: 'submit' | 'button' | 'reset';
  /** Whether this is the primary action */
  primary?: boolean;
  /** Click handler for non-submit buttons */
  onClick?: () => void;
}

/**
 * Props for the Form component
 */
export interface FormProps<Values extends FormikValues = FormikValues> 
  extends Omit<FormikConfig<Values>, 'children'> {
  /** Form content */
  children: ReactNode | ((formikProps: any) => ReactNode);
  /** Form title */
  title?: string;
  /** Form description */
  description?: string;
  /** Form actions/buttons */
  actions?: FormAction[];
  /** Whether to show default submit button */
  showSubmitButton?: boolean;
  /** Submit button text */
  submitButtonText?: string;
  /** Whether to show cancel button */
  showCancelButton?: boolean;
  /** Cancel button text */
  cancelButtonText?: string;
  /** Cancel button click handler */
  onCancel?: () => void;
  /** Whether actions should be full width on mobile */
  fullWidthActions?: boolean;
  /** Custom form class name */
  className?: string;
  /** Whether to disable form during submission */
  disableOnSubmit?: boolean;
  /** Custom error display component */
  errorComponent?: (error: string) => ReactNode;
  /** Whether to show form-level errors */
  showFormErrors?: boolean;
  /** Form layout direction */
  layout?: 'vertical' | 'horizontal';
  /** Loading state override */
  loading?: boolean;
}

/**
 * Enhanced Form component with Formik integration and comprehensive functionality
 * 
 * Features:
 * - Complete Formik integration with validation
 * - Built-in loading states and error handling
 * - Customizable action buttons
 * - Form-level error display
 * - Responsive design with glassmorphism
 * - Accessibility support
 * - Flexible layout options
 * 
 * @example
 * ```tsx
 * <Form
 *   initialValues={{ email: '', password: '' }}
 *   validationSchema={loginSchema}
 *   onSubmit={handleSubmit}
 *   title="Sign In"
 *   description="Enter your credentials to access your account"
 *   submitButtonText="Sign In"
 *   showCancelButton
 *   onCancel={() => navigate('/home')}
 * >
 *   <Input name="email" label="Email" type="email" />
 *   <Input name="password" label="Password" type="password" />
 * </Form>
 * ```
 */
export const Form = <Values extends FormikValues = FormikValues>({
  children,
  title,
  description,
  actions,
  showSubmitButton = true,
  submitButtonText = 'Submit',
  showCancelButton = false,
  cancelButtonText = 'Cancel',
  onCancel,
  fullWidthActions = false,
  className = '',
  disableOnSubmit = true,
  errorComponent,
  showFormErrors = true,
  layout = 'vertical',
  loading: loadingProp,
  ...formikProps
}: FormProps<Values>) => {
  return (
    <Formik {...formikProps}>
      {(formik) => {
        const isLoading = loadingProp ?? formik.isSubmitting;
        const hasFormErrors = showFormErrors && formik.errors && typeof formik.errors === 'string';
        const isDisabled = disableOnSubmit && isLoading;

        return (
          <FormikForm 
            className={`space-y-6 ${className}`}
            noValidate
          >
            {/* Form Header */}
            {(title || description) && (
              <div className="text-center space-y-2">
                {title && (
                  <h2 className="text-2xl font-bold text-text-primary">
                    {title}
                  </h2>
                )}
                {description && (
                  <p className="text-text-secondary">
                    {description}
                  </p>
                )}
              </div>
            )}

            {/* Form-level Errors */}
            {hasFormErrors && (
              <div className="rounded-lg bg-danger/10 border border-danger/20 p-4">
                <div className="flex items-start">
                  <Icon name="alert" className="text-danger mt-0.5 flex-shrink-0" size={16} />
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-danger">
                      Form Error
                    </h3>
                    <div className="mt-1 text-sm text-danger/80">
                      {errorComponent ? (
                        errorComponent(formik.errors as string)
                      ) : (
                        <p>{formik.errors as string}</p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Form Fields */}
            <div className={`space-y-4 ${isDisabled ? 'pointer-events-none opacity-75' : ''}`}>
              {typeof children === 'function' ? children(formik) : children}
            </div>

            {/* Form Actions */}
            {(actions || showSubmitButton || showCancelButton) && (
              <div className={`flex gap-3 ${
                fullWidthActions 
                  ? 'flex-col sm:flex-row' 
                  : 'flex-col sm:flex-row sm:justify-end'
              }`}>
                {/* Custom Actions */}
                {actions?.map((action, index) => {
                  const { label, type = 'button', primary, onClick, ...actionProps } = action;
                  
                  return (
                    <Button
                      key={index}
                      type={type}
                      variant={primary ? 'primary' : 'secondary'}
                      loading={type === 'submit' ? isLoading : false}
                      disabled={isDisabled}
                      onClick={type === 'button' ? onClick : undefined}
                      className={fullWidthActions ? 'w-full sm:w-auto' : ''}
                      {...actionProps}
                    >
                      {label}
                    </Button>
                  );
                })}

                {/* Default Cancel Button */}
                {showCancelButton && !actions?.some(a => a.type === 'button') && (
                  <Button
                    type="button"
                    variant="ghost"
                    onClick={onCancel}
                    disabled={isLoading}
                    className={fullWidthActions ? 'w-full sm:w-auto' : ''}
                  >
                    {cancelButtonText}
                  </Button>
                )}

                {/* Default Submit Button */}
                {showSubmitButton && !actions?.some(a => a.type === 'submit') && (
                  <Button
                    type="submit"
                    variant="outline"
                    loading={isLoading}
                    disabled={isDisabled || !formik.isValid}
                    className={fullWidthActions ? 'w-full sm:w-auto' : ''}
                  >
                    {submitButtonText}
                  </Button>
                )}
              </div>
            )}
          </FormikForm>
        );
      }}
    </Formik>
  );
};

/**
 * Form field wrapper component for consistent spacing and layout
 */
export interface FormFieldProps {
  children: ReactNode;
  className?: string;
}

export const FormField: React.FC<FormFieldProps> = ({ 
  children, 
  className = '' 
}) => {
  return (
    <div className={`space-y-1 ${className}`}>
      {children}
    </div>
  );
};

/**
 * Form section component for grouping related fields
 */
export interface FormSectionProps {
  title?: string;
  description?: string;
  children: ReactNode;
  className?: string;
}

export const FormSection: React.FC<FormSectionProps> = ({
  title,
  description,
  children,
  className = ''
}) => {
  return (
    <div className={`space-y-4 ${className}`}>
      {(title || description) && (
        <div className="space-y-1">
          {title && (
            <h3 className="text-lg font-medium text-text-primary">
              {title}
            </h3>
          )}
          {description && (
            <p className="text-sm text-text-secondary">
              {description}
            </p>
          )}
        </div>
      )}
      <div className="space-y-4">
        {children}
      </div>
    </div>
  );
};

export default Form;
