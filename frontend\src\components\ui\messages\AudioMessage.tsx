// frontend/src/components/ui/messages/AudioMessage.tsx
import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Icon } from '../Icon';

/**
 * Props for the AudioMessage component
 */
export interface AudioMessageProps {
  /** Audio file URL */
  src: string;
  /** Audio duration in seconds */
  duration?: number;
  /** Whether this message is from the current user */
  isOwn?: boolean;
  /** Custom class name */
  className?: string;
  /** Whether to show waveform visualization */
  showWaveform?: boolean;
  /** Waveform data points (0-1 values) */
  waveformData?: number[];
  /** Loading state */
  loading?: boolean;
  /** Error state */
  error?: boolean;
  /** Error message */
  errorMessage?: string;
  /** Retry handler */
  onRetry?: () => void;
}

/**
 * Format time in MM:SS format
 */
const formatTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins}:${secs.toString().padStart(2, '0')}`;
};

/**
 * Enhanced AudioMessage component for audio playback
 * 
 * Features:
 * - Audio playback controls
 * - Progress bar with seeking
 * - Duration display
 * - Waveform visualization (optional)
 * - Loading and error states
 * - Glassmorphism design system integration
 * 
 * @example
 * ```tsx
 * <AudioMessage 
 *   src="/path/to/audio.mp3"
 *   duration={120}
 *   isOwn={false}
 *   showWaveform
 *   waveformData={waveformPoints}
 * />
 * ```
 */
export const AudioMessage: React.FC<AudioMessageProps> = ({
  src,
  duration: durationProp,
  isOwn = false,
  className = '',
  showWaveform = false,
  waveformData = [],
  loading = false,
  error = false,
  errorMessage,
  onRetry,
}) => {
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(durationProp || 0);
  const [isLoading, setIsLoading] = useState(loading);

  // Handle play/pause
  const togglePlayback = useCallback(async () => {
    if (!audioRef.current) return;

    try {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        await audioRef.current.play();
      }
    } catch (error) {
      console.error('Audio playback error:', error);
    }
  }, [isPlaying]);

  // Handle time update
  const handleTimeUpdate = useCallback(() => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime);
    }
  }, []);

  // Handle loaded metadata
  const handleLoadedMetadata = useCallback(() => {
    if (audioRef.current) {
      setDuration(audioRef.current.duration);
      setIsLoading(false);
    }
  }, []);

  // Handle seek
  const handleSeek = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    if (!audioRef.current) return;

    const rect = e.currentTarget.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const percentage = clickX / rect.width;
    const newTime = percentage * duration;

    audioRef.current.currentTime = newTime;
    setCurrentTime(newTime);
  }, [duration]);

  // Audio event listeners
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handlePlay = () => setIsPlaying(true);
    const handlePause = () => setIsPlaying(false);
    const handleEnded = () => {
      setIsPlaying(false);
      setCurrentTime(0);
    };

    audio.addEventListener('play', handlePlay);
    audio.addEventListener('pause', handlePause);
    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('loadedmetadata', handleLoadedMetadata);

    return () => {
      audio.removeEventListener('play', handlePlay);
      audio.removeEventListener('pause', handlePause);
      audio.removeEventListener('ended', handleEnded);
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
    };
  }, [handleTimeUpdate, handleLoadedMetadata]);

  // Container classes
  const containerClasses = [
    'inline-block max-w-sm',
    isOwn ? 'ml-auto' : '',
    className,
  ].filter(Boolean).join(' ');

  // Audio player classes
  const playerClasses = [
    'flex items-center p-3 rounded-lg',
    'glass-light border border-border-glass',
    'transition-all duration-200',
    error ? 'border-danger/50 bg-danger/5' : '',
  ].filter(Boolean).join(' ');

  const progress = duration > 0 ? (currentTime / duration) * 100 : 0;

  if (error) {
    return (
      <div className={containerClasses}>
        <div className={playerClasses}>
          <div className="flex-shrink-0 w-10 h-10 rounded-lg bg-danger/10 flex items-center justify-center">
            <Icon name="alertTriangle" className="text-danger" size={20} />
          </div>
          <div className="flex-1 ml-3">
            <p className="text-sm text-danger">
              {errorMessage || 'Failed to load audio'}
            </p>
            {onRetry && (
              <button
                onClick={onRetry}
                className="text-xs text-primary hover:text-primary-hover underline mt-1"
              >
                Try again
              </button>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={containerClasses}>
      <div className={playerClasses}>
        {/* Hidden audio element */}
        <audio ref={audioRef} src={src} preload="metadata" />

        {/* Play/pause button */}
        <button
          onClick={togglePlayback}
          disabled={isLoading}
          className="flex-shrink-0 w-10 h-10 rounded-full bg-primary hover:bg-primary-hover disabled:opacity-50 flex items-center justify-center transition-colors"
        >
          {isLoading ? (
            <Icon name="spinner" className="animate-spin text-white" size={16} />
          ) : (
            <Icon 
              name={isPlaying ? 'pause' : 'play'} 
              className="text-white" 
              size={16} 
            />
          )}
        </button>

        {/* Audio info and controls */}
        <div className="flex-1 ml-3">
          {/* Waveform or progress bar */}
          <div className="mb-2">
            {showWaveform && waveformData.length > 0 ? (
              <div 
                className="flex items-end h-8 space-x-0.5 cursor-pointer"
                onClick={handleSeek}
              >
                {waveformData.map((amplitude, index) => (
                  <div
                    key={index}
                    className={`flex-1 rounded-sm transition-colors ${
                      (index / waveformData.length) * 100 <= progress
                        ? 'bg-primary'
                        : 'bg-bg-tertiary'
                    }`}
                    style={{ height: `${Math.max(amplitude * 100, 10)}%` }}
                  />
                ))}
              </div>
            ) : (
              <div 
                className="h-2 bg-bg-tertiary rounded-full cursor-pointer overflow-hidden"
                onClick={handleSeek}
              >
                <div 
                  className="h-full bg-primary transition-all duration-100"
                  style={{ width: `${progress}%` }}
                />
              </div>
            )}
          </div>

          {/* Time display */}
          <div className="flex items-center justify-between text-xs text-text-secondary">
            <span>{formatTime(currentTime)}</span>
            <span>{formatTime(duration)}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AudioMessage;
