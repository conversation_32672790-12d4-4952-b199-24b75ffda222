import fs from "fs";
import path from "path";
import { parse } from "@babel/parser";
import traverseModule from "@babel/traverse";
const traverse = traverseModule.default;


const SLICES_DIR = "frontend/src/store/slices"; // adjust if needed
const OUTPUT_FILE = "slices-with-actions.json";

function getActionsFromSlice(filePath) {
  const code = fs.readFileSync(filePath, "utf8");

  let actions = [];
  try {
    const ast = parse(code, {
      sourceType: "module",
      plugins: ["typescript", "jsx"]
    });

    traverse(ast, {
      ObjectExpression(path) {
        const parent = path.parentPath.node;
        if (
          parent.type === "CallExpression" &&
          parent.callee.name === "createSlice"
        ) {
          const reducersProp = path.node.properties.find(
            (prop) => prop.key && prop.key.name === "reducers"
          );
          if (reducersProp && reducersProp.value.type === "ObjectExpression") {
            reducersProp.value.properties.forEach((prop) => {
              if (prop.key && prop.key.name) {
                actions.push(prop.key.name);
              }
            });
          }
        }
      }
    });
  } catch (err) {
    console.error(`❌ Failed to parse ${filePath}`, err.message);
  }

  return actions;
}

function walkDir(dir) {
  const files = fs.readdirSync(dir);
  const slices = {};

  for (const file of files) {
    const fullPath = path.join(dir, file);
    if (fs.lstatSync(fullPath).isDirectory()) {
      Object.assign(slices, walkDir(fullPath));
    } else if (file.endsWith(".ts")) {
      const actions = getActionsFromSlice(fullPath);
      if (actions.length > 0) {
        slices[file] = actions;
      }
    }
  }

  return slices;
}

const slices = walkDir(SLICES_DIR);
fs.writeFileSync(OUTPUT_FILE, JSON.stringify(slices, null, 2));
console.log(`✅ Slice actions written to ${OUTPUT_FILE}`);
