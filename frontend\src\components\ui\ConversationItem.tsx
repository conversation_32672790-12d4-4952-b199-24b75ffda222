// frontend/src/components/ui/ConversationItem.tsx
import React from 'react';
import { ProfilePhotoAvatar } from './ProfilePhotoAvatar';
import { Icon } from './Icon';

/**
 * Conversation participant interface
 */
export interface ConversationParticipant {
  id: string;
  name: string;
  avatar?: string;
  status?: 'online' | 'offline' | 'away' | 'busy';
}

/**
 * Last message interface
 */
export interface LastMessage {
  id: string;
  content: string;
  type: 'text' | 'image' | 'file' | 'audio' | 'system';
  senderId: string;
  senderName: string;
  timestamp: Date;
  isOwn: boolean;
}

/**
 * Props for the ConversationItem component
 */
export interface ConversationItemProps {
  /** Conversation ID */
  id: string;
  /** Conversation name/title */
  name: string;
  /** Whether this is a group conversation */
  isGroup?: boolean;
  /** Conversation participants */
  participants: ConversationParticipant[];
  /** Last message in the conversation */
  lastMessage?: LastMessage;
  /** Number of unread messages */
  unreadCount?: number;
  /** Whether the conversation is currently selected */
  isSelected?: boolean;
  /** Whether the conversation is muted */
  isMuted?: boolean;
  /** Whether the conversation is pinned */
  isPinned?: boolean;
  /** Whether someone is typing */
  isTyping?: boolean;
  /** Names of users who are typing */
  typingUsers?: string[];
  /** Conversation avatar (for groups) */
  avatar?: string;
  /** Whether the conversation is archived */
  isArchived?: boolean;
  /** Custom class name */
  className?: string;
  /** Click handler */
  onClick?: () => void;
  /** Context menu handler */
  onContextMenu?: (e: React.MouseEvent) => void;
  /** Long press handler for mobile */
  onLongPress?: () => void;
}

/**
 * Enhanced ConversationItem component for conversation lists
 * 
 * Features:
 * - Support for both individual and group conversations
 * - Unread message count badges
 * - Typing indicators
 * - Online status indicators
 * - Muted and pinned conversation indicators
 * - Last message preview with different message types
 * - Glassmorphism design integration
 * - Responsive design for mobile and desktop
 * 
 * @example
 * ```tsx
 * <ConversationItem
 *   id="conv-123"
 *   name="John Doe"
 *   participants={[{ id: "user-1", name: "John Doe", status: "online" }]}
 *   lastMessage={{
 *     content: "Hey, how are you?",
 *     type: "text",
 *     timestamp: new Date(),
 *     isOwn: false
 *   }}
 *   unreadCount={3}
 *   onClick={() => selectConversation("conv-123")}
 * />
 * ```
 */
export const ConversationItem: React.FC<ConversationItemProps> = ({
  id,
  name,
  isGroup = false,
  participants,
  lastMessage,
  unreadCount = 0,
  isSelected = false,
  isMuted = false,
  isPinned = false,
  isTyping = false,
  typingUsers = [],
  avatar,
  isArchived = false,
  className = '',
  onClick,
  onContextMenu,
  onLongPress,
}) => {
  // Get the primary participant for individual conversations
  const primaryParticipant = !isGroup && participants.length > 0 ? participants[0] : null;

  // Format last message content based on type
  const formatLastMessage = (message: LastMessage): string => {
    switch (message.type) {
      case 'image':
        return '📷 Photo';
      case 'file':
        return '📎 File';
      case 'audio':
        return '🎵 Audio';
      case 'system':
        return message.content;
      default:
        return message.content;
    }
  };

  // Format timestamp
  const formatTimestamp = (date: Date): string => {
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return 'now';
    } else if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 24 * 7) {
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  // Get typing indicator text
  const getTypingText = (): string => {
    if (!isTyping || typingUsers.length === 0) return '';
    
    if (typingUsers.length === 1) {
      return `${typingUsers[0]} is typing...`;
    } else if (typingUsers.length === 2) {
      return `${typingUsers[0]} and ${typingUsers[1]} are typing...`;
    } else {
      return `${typingUsers.length} people are typing...`;
    }
  };

  const containerClasses = [
    'flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-colors',
    isSelected ? 'bg-gray-700' : 'hover:bg-gray-750',
    isArchived ? 'opacity-60' : '',
    className,
  ].filter(Boolean).join(' ');

  return (
    <div
      className={containerClasses}
      onClick={onClick}
      onContextMenu={onContextMenu}
      data-conversation-id={id}
    >
      {/* Avatar */}
      <div className="relative">
        {isGroup ? (
          <ProfilePhotoAvatar
            name={name}
            isGroup={true}
            groupMembers={participants.map(p => p.name)}
            size="medium"
            className="w-12 h-12"
          />
        ) : (
          <ProfilePhotoAvatar
            src={primaryParticipant?.avatar || avatar}
            name={primaryParticipant?.name || name}
            status={primaryParticipant?.status || 'offline'}
            size="medium"
            showStatus={true}
            className="w-12 h-12"
          />
        )}
      </div>

      {/* Content */}
      <div className="flex-1 min-w-0">
        {/* Header */}
        <div className="flex items-center justify-between">
          <h4 className="text-white font-medium truncate">
            {name}
          </h4>
          <span className="text-xs text-gray-400">
            {lastMessage ? formatTimestamp(lastMessage.timestamp) : ''}
          </span>
        </div>

        {/* Last Message or Typing Indicator */}
        <p className="text-sm text-gray-400 truncate">
          {isTyping ? 'Typing...' : lastMessage ? formatLastMessage(lastMessage) : 'No messages yet'}
        </p>
      </div>

      {/* Unread Count Badge */}
      {unreadCount > 0 && (
        <div className="w-5 h-5 bg-teal-500 rounded-full flex items-center justify-center">
          <span className="text-xs text-white">{unreadCount > 99 ? '99+' : unreadCount}</span>
        </div>
      )}
    </div>
  );
};

export default ConversationItem;
