// frontend/src/components/ui/Sidebar.tsx
import React, { useState, useMemo, useCallback } from 'react';
import { Button } from './Button';
import { SearchBar, type SearchResult } from './SearchBar';
import { ConversationItem, type ConversationItemProps } from './ConversationItem';
import { Icon } from './Icon';
import { useGetConversationsQuery, useSearchUsersQuery } from '../../services';
import { useAuth } from '../../contexts/AuthContext';
import type { Conversation } from '../../services/conversationApi';
import type { SearchUser } from '../../services/userApi';

/**
 * Sidebar tab types
 */
export type SidebarTab = 'conversations' | 'contacts' | 'archived';

/**
 * Props for the Sidebar component
 */
export interface SidebarProps {
  /** Current active tab */
  activeTab?: SidebarTab;
  /** Currently selected conversation ID */
  selectedConversationId?: string;
  /** Recent search queries */
  recentSearches?: string[];
  /** Whether the sidebar is collapsed (mobile) */
  isCollapsed?: boolean;
  /** Custom class name */
  className?: string;
  /** Tab change handler */
  onTabChange?: (tab: SidebarTab) => void;
  /** Search result selection handler */
  onSearchResultSelect?: (result: SearchResult) => void;
  /** Conversation selection handler */
  onConversationSelect?: (conversationId: string) => void;
  /** New conversation handler */
  onNewConversation?: () => void;
  /** Settings handler */
  onSettings?: () => void;
  /** Archive handler */
  onArchive?: () => void;
}

/**
 * Enhanced Sidebar component for chat navigation
 * 
 * Features:
 * - Tabbed interface (Conversations, Contacts, Archived)
 * - Universal search with results dropdown
 * - Conversation list with unread counts and status
 * - New conversation and settings buttons
 * - Responsive design with collapse support
 * - Glassmorphism design integration
 * - Keyboard navigation support
 * 
 * @example
 * ```tsx
 * <Sidebar
 *   activeTab="conversations"
 *   conversations={conversationList}
 *   selectedConversationId="conv-123"
 *   onTabChange={handleTabChange}
 *   onSearch={handleSearch}
 *   onConversationSelect={handleConversationSelect}
 *   onNewConversation={handleNewConversation}
 * />
 * ```
 */
export const Sidebar: React.FC<SidebarProps> = ({
  activeTab = 'conversations',
  selectedConversationId,
  recentSearches = [],
  isCollapsed = false,
  className = '',
  onTabChange,
  onSearchResultSelect,
  onConversationSelect,
  onNewConversation,
  onSettings,
}) => {
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');

  // Fetch conversations using RTK Query
  const {
    data: conversationsData,
    isLoading: conversationsLoading,
    error: conversationsError,
    refetch: refetchConversations
  } = useGetConversationsQuery();

  // Search users using RTK Query (only when search query exists)
  const {
    data: searchUsersData,
    isLoading: searchLoading,
    error: searchError
  } = useSearchUsersQuery(searchQuery, {
    skip: searchQuery.length < 2
  });

  // Handle search
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  // Handle search result selection
  const handleSearchResultSelect = useCallback((result: SearchResult) => {
    setSearchQuery('');
    onSearchResultSelect?.(result);
  }, [onSearchResultSelect]);

  // Handle conversation click
  const handleConversationClick = useCallback((conversation: ConversationItemProps) => {
    onConversationSelect?.(conversation.id);
  }, [onConversationSelect]);
  
  // Transform API conversations to ConversationItemProps
  const transformedConversations = useMemo(() => {
    if (!conversationsData?.results) return [];

    return conversationsData.results.map((conv: Conversation): ConversationItemProps => {
      // Get conversation name
      const conversationName = conv.name ||
        (conv.type === 'DIRECT' && conv.participants.length > 0
          ? conv.participants
            .filter(p => p.id !== user?.id)
            .map(p => p.name || 'Unknown User')
            .join(', ')
          : `Group Chat`);

      // Transform participants
      const participants = conv.participants.map(p => ({
        id: p.id,
        name: p.name,
        avatar: p.profilePicture,
        status: 'offline' as const // We don't have real-time status from this API
      }));

      // Transform last message
      const lastMessage = conv.lastMessage ? {
        id: conv.lastMessage.id,
        content: conv.lastMessage.content,
        type: 'text' as const,
        senderId: conv.lastMessage.sender.id,
        senderName: conv.lastMessage.sender.name,
        timestamp: new Date(conv.lastMessage.createdAt),
        isOwn: false // We'd need to check against current user
      } : undefined;

      return {
        id: conv.id,
        name: conversationName,
        isGroup: conv.type === 'GROUP',
        participants,
        lastMessage,
        unreadCount: 0, // Not provided by API
        isArchived: false, // Not provided by API
        isPinned: false, // Not provided by API
        isMuted: false, // Not provided by API
        isTyping: false, // Real-time data
        typingUsers: []
      };
    });
  }, [conversationsData, user?.id]);

  const usernameExtractor = (email: string) => {
    return email.split('@')[0];
  };

  // Transform search results
  const searchResults = useMemo(() => {
    if (!searchUsersData?.results || searchQuery.length < 2) return [];

    return searchUsersData.results.map((user: SearchUser): SearchResult => ({
      id: user.id,
      type: 'user',
      title: user.name,
      subtitle: `@${usernameExtractor(user.email)}`,
      avatar: user.profile_picture
    }));
  }, [searchUsersData, searchQuery]);

  console.log('transformedConversations', transformedConversations);
  // Filter conversations based on active tab
  const filteredConversations = transformedConversations.filter((conv: any) => {
    switch (activeTab) {
      case 'archived':
        return conv.isArchived;
      case 'conversations':
      default:
        return !conv.isArchived;
    }
  });

  // Sort conversations (pinned first, then by last message timestamp)
  const sortedConversations = [...filteredConversations].sort((a, b) => {
    // Pinned conversations first
    if (a.isPinned && !b.isPinned) return -1;
    if (!a.isPinned && b.isPinned) return 1;

    // Then by last message timestamp
    if (a.lastMessage && b.lastMessage) {
      return b.lastMessage.timestamp.getTime() - a.lastMessage.timestamp.getTime();
    }
    if (a.lastMessage && !b.lastMessage) return -1;
    if (!a.lastMessage && b.lastMessage) return 1;

    return 0;
  });

  const sidebarClasses = [
    'flex flex-col h-full glass-light border-r border-border-glass',
    isCollapsed ? 'w-16' : 'w-80',
    'transition-all duration-300',
    className,
  ].filter(Boolean).join(' ');

  const tabClasses = (tab: SidebarTab) => [
    'flex-1 py-2 px-4 text-sm font-medium transition-colors',
    'border-b-2 transition-colors',
    activeTab === tab
      ? 'text-primary border-primary'
      : 'text-text-secondary border-transparent hover:text-text-primary hover:border-border-glass',
  ].join(' ');

  // Show loading state for conversations
  if (conversationsLoading && !conversationsData) {
    return (
      <div className={sidebarClasses}>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <Icon name="loader" className="animate-spin text-text-secondary mb-2" size={24} />
            <p className="text-text-secondary">Loading conversations...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show error state for conversations
  if (conversationsError && !conversationsData) {
    return (
      <div className={sidebarClasses}>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <Icon name="alert-circle" className="text-danger mb-2" size={24} />
            <p className="text-danger">Failed to load conversations</p>
            <p className="text-text-secondary text-sm mb-3">
              {conversationsError && typeof conversationsError === 'object' && 'data' in conversationsError
                ? (conversationsError.data as any)?.error || 'Network error occurred'
                : 'Network error occurred'
              }
            </p>
            <Button
              variant="outline"
              size="small"
              className="mt-2"
              onClick={() => refetchConversations()}
            >
              Retry
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (isCollapsed) {
    return (
      <div className={sidebarClasses}>
        {/* Collapsed sidebar with icons only */}
        <div className="p-4 space-y-4">
          <Button
            variant="ghost"
            size="small"
            icon="message-circle"
            onClick={() => onTabChange?.('conversations')}
            className={activeTab === 'conversations' ? 'text-primary' : ''}
          />
          <Button
            variant="ghost"
            size="small"
            icon="users"
            onClick={() => onTabChange?.('contacts')}
            className={activeTab === 'contacts' ? 'text-primary' : ''}
          />
          <Button
            variant="ghost"
            size="small"
            icon="archive"
            onClick={() => onTabChange?.('archived')}
            className={activeTab === 'archived' ? 'text-primary' : ''}
          />
        </div>

        <div className="mt-auto p-4 space-y-2">
          <Button
            variant="ghost"
            size="small"
            icon="plus"
            onClick={onNewConversation}
          />
          <Button
            variant="ghost"
            size="small"
            icon="settings"
            onClick={onSettings}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-2">

      {/* Simple Conversation List */}
      {sortedConversations.length > 0 ? (
        sortedConversations.map(conversation => (
          <ConversationItem
            key={conversation.id}
            {...conversation}
            isSelected={conversation.id === selectedConversationId}
            onClick={() => handleConversationClick(conversation)}
          />
        ))
      ) : (
        <div className="p-8 text-center">
          <Icon
            name="message-circle"
            className="mx-auto text-gray-500 mb-3"
            size={32}
          />
          <h3 className="text-sm font-medium text-gray-400 mb-1">
            No conversations yet
          </h3>
          <p className="text-xs text-gray-500">
            Start a new conversation to get started
          </p>
        </div>
      )}
    </div>
  );
};

export default Sidebar;
