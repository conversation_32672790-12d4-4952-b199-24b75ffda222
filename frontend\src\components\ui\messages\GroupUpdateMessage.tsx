// frontend/src/components/ui/messages/GroupUpdateMessage.tsx
import React from 'react';
import { Icon } from '../Icon';
import { ProfilePhotoAvatar } from '../ProfilePhotoAvatar';

/**
 * Group update types
 */
export type GroupUpdateType = 
  | 'member_added'
  | 'member_removed' 
  | 'member_left'
  | 'name_changed'
  | 'description_changed'
  | 'avatar_changed'
  | 'admin_added'
  | 'admin_removed'
  | 'group_created';

/**
 * Props for the GroupUpdateMessage component
 */
export interface GroupUpdateMessageProps {
  /** Type of group update */
  type: GroupUpdateType;
  /** User who performed the action */
  actor?: {
    id: string;
    name: string;
    avatar?: string;
  };
  /** User who was affected by the action */
  target?: {
    id: string;
    name: string;
    avatar?: string;
  };
  /** Previous value (for changes) */
  previousValue?: string;
  /** New value (for changes) */
  newValue?: string;
  /** Timestamp of the update */
  timestamp: Date;
  /** Custom class name */
  className?: string;
}

/**
 * Enhanced GroupUpdateMessage component for system messages
 * 
 * Features:
 * - Different update types with appropriate icons
 * - User avatars for relevant updates
 * - Formatted text based on update type
 * - Timestamp display
 * - Glassmorphism design system integration
 * 
 * @example
 * ```tsx
 * <GroupUpdateMessage 
 *   type="member_added"
 *   actor={{ id: "1", name: "John Doe" }}
 *   target={{ id: "2", name: "Jane Smith" }}
 *   timestamp={new Date()}
 * />
 * ```
 */
export const GroupUpdateMessage: React.FC<GroupUpdateMessageProps> = ({
  type,
  actor,
  target,
  previousValue,
  newValue,
  timestamp,
  className = '',
}) => {
  // Get icon for update type
  const getUpdateIcon = (updateType: GroupUpdateType): string => {
    switch (updateType) {
      case 'member_added':
        return 'userPlus';
      case 'member_removed':
        return 'userMinus';
      case 'member_left':
        return 'userX';
      case 'name_changed':
        return 'edit';
      case 'description_changed':
        return 'edit';
      case 'avatar_changed':
        return 'image';
      case 'admin_added':
        return 'shield';
      case 'admin_removed':
        return 'shieldOff';
      case 'group_created':
        return 'users';
      default:
        return 'info';
    }
  };

  // Generate message text
  const getMessageText = (): string => {
    switch (type) {
      case 'member_added':
        return `${actor?.name || 'Someone'} added ${target?.name || 'someone'} to the group`;
      case 'member_removed':
        return `${actor?.name || 'Someone'} removed ${target?.name || 'someone'} from the group`;
      case 'member_left':
        return `${actor?.name || 'Someone'} left the group`;
      case 'name_changed':
        return `${actor?.name || 'Someone'} changed the group name${newValue ? ` to "${newValue}"` : ''}`;
      case 'description_changed':
        return `${actor?.name || 'Someone'} changed the group description`;
      case 'avatar_changed':
        return `${actor?.name || 'Someone'} changed the group photo`;
      case 'admin_added':
        return `${actor?.name || 'Someone'} made ${target?.name || 'someone'} a group admin`;
      case 'admin_removed':
        return `${actor?.name || 'Someone'} removed ${target?.name || 'someone'} as group admin`;
      case 'group_created':
        return `${actor?.name || 'Someone'} created the group`;
      default:
        return 'Group was updated';
    }
  };

  const icon = getUpdateIcon(type);
  const messageText = getMessageText();

  return (
    <div className={`flex justify-center my-4 ${className}`}>
      <div className="flex items-center max-w-md px-4 py-2 rounded-lg glass-light border border-border-glass">
        {/* Update icon */}
        <div className="flex-shrink-0 w-6 h-6 rounded-full bg-bg-tertiary flex items-center justify-center">
          <Icon name={icon} className="text-text-secondary" size={12} />
        </div>

        {/* Message content */}
        <div className="flex-1 ml-3 text-center">
          <p className="text-sm text-text-secondary">
            {messageText}
          </p>
          
          {/* Show affected users for relevant updates */}
          {(type === 'member_added' || type === 'member_removed' || type === 'admin_added' || type === 'admin_removed') && (
            <div className="flex items-center justify-center space-x-2 mt-2">
              {actor && (
                <ProfilePhotoAvatar
                  src={actor.avatar}
                  name={actor.name}
                  size="small"
                />
              )}
              {target && (
                <>
                  <Icon name="arrowRight" className="text-text-tertiary" size={12} />
                  <ProfilePhotoAvatar
                    src={target.avatar}
                    name={target.name}
                    size="small"
                  />
                </>
              )}
            </div>
          )}

          {/* Timestamp */}
          <p className="text-xs text-text-tertiary mt-1">
            {timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </p>
        </div>
      </div>
    </div>
  );
};

export default GroupUpdateMessage;
