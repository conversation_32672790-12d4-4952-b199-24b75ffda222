import React, { useState } from 'react';
import { X, Users, Search, Check } from 'lucide-react';
import { useDispatch } from 'react-redux';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { LoadingSpinner } from '../ui/LoadingSpinner';
import { ApiErrorDisplay } from '../ui/ApiErrorDisplay';
import { useSearchUsersQuery } from '../../services';
import { useDebounce } from '../../hooks/useDebounce';
import { createDraftGroup } from '../../store/slices/conversationSlice';
import type { SearchUser } from '../../services/userApi';

interface CreateGroupDialogProps {
  open: boolean;
  onClose: () => void;
  onGroupCreated?: (draftId: string) => void;
  currentUser: SearchUser;
}

const CreateGroupDialog: React.FC<CreateGroupDialogProps> = ({
  open,
  onClose,
  onGroupCreated,
  currentUser,
}) => {
  const dispatch = useDispatch();
  const [groupName, setGroupName] = useState('');
  const [groupDescription, setGroupDescription] = useState('');
  const [maxParticipants, setMaxParticipants] = useState(50);
  const [isPublic, setIsPublic] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedMembers, setSelectedMembers] = useState<SearchUser[]>([]);
  const [loading, setLoading] = useState(false);

  // Debounce the search query to avoid too many API calls
  const debouncedQuery = useDebounce(searchQuery, 300);

  // Use RTK Query for user search
  const {
    data: searchData,
    error: searchError,
    isLoading: isSearching,
    refetch: retrySearch
  } = useSearchUsersQuery(debouncedQuery, {
    skip: debouncedQuery.length < 2,
  });

  const searchResults = searchData?.results || [];
  // Filter out current user and already selected members
  const filteredResults = searchResults.filter(
    (user) =>
      user.id !== currentUser.id &&
      !selectedMembers.some((member) => member.id === user.id)
  );

  const handleAddMember = (user: SearchUser) => {
    if (selectedMembers.length >= maxParticipants - 1) {
      alert(`Maximum ${maxParticipants - 1} members can be added initially`);
      return;
    }

    setSelectedMembers([...selectedMembers, user]);
    setSearchQuery('');
  };

  const handleRemoveMember = (userId: string) => {
    setSelectedMembers(selectedMembers.filter((member) => member.id !== userId));
  };

  const handleCreateGroup = () => {
    if (!groupName.trim()) {
      alert('Group name is required');
      return;
    }

    try {
      // Create draft group instead of making API call
      dispatch(createDraftGroup({
        name: groupName.trim(),
        description: groupDescription.trim() || undefined,
        participants: selectedMembers,
        maxParticipants,
        isPublic,
      }));

      // Get the draft ID that would be generated
      const draftId = `draft-group-${Date.now()}`;

      // Reset form
      setGroupName('');
      setGroupDescription('');
      setMaxParticipants(50);
      setIsPublic(false);
      setSelectedMembers([]);
      setSearchQuery('');

      if (onGroupCreated) {
        onGroupCreated(draftId);
      }
      onClose();
    } catch (error) {
      console.error('Failed to create draft group:', error);
      alert('Failed to create group. Please try again.');
    }
  };

  const handleClose = () => {
    if (!loading) {
      setGroupName('');
      setGroupDescription('');
      setMaxParticipants(50);
      setIsPublic(false);
      setSelectedMembers([]);
      setSearchQuery('');
      onClose();
    }
  };

  if (!open) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-lg max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-3">
            <Users className="w-6 h-6 text-blue-600" />
            <h2 className="text-xl font-semibold text-gray-900">Create Group</h2>
          </div>
          <button
            onClick={handleClose}
            disabled={loading}
            className="text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {/* Group Details */}
          <div className="space-y-4 mb-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Group Name *
              </label>
              <Input
                value={groupName}
                onChange={(e) => setGroupName(e.target.value)}
                placeholder="Enter group name"
                className="w-full"
                disabled={loading}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                value={groupDescription}
                onChange={(e) => setGroupDescription(e.target.value)}
                placeholder="Enter group description (optional)"
                rows={3}
                disabled={loading}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Max Members
                </label>
                <Input
                  type="number"
                  value={maxParticipants}
                  onChange={(e) => setMaxParticipants(Math.max(2, Math.min(500, parseInt(e.target.value) || 50)))}
                  min={2}
                  max={500}
                  disabled={loading}
                  className="w-full"
                />
              </div>

              <div className="flex items-center space-x-2 pt-6">
                <input
                  type="checkbox"
                  id="isPublic"
                  checked={isPublic}
                  onChange={(e) => setIsPublic(e.target.checked)}
                  disabled={loading}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label htmlFor="isPublic" className="text-sm font-medium text-gray-700">
                  Public Group
                </label>
              </div>
            </div>
          </div>

          {/* Member Search */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Add Members
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search users by name or username"
                className="pl-10 w-full"
                disabled={loading}
              />
            </div>

            {/* Search Results */}
            {isSearching && (
              <LoadingSpinner
                text="Searching..."
                centered
                className="py-4"
              />
            )}

            {searchError && (
              <div className="mt-2">
                <ApiErrorDisplay
                  error={searchError}
                  onRetry={retrySearch}
                  size="sm"
                />
              </div>
            )}

            {!isSearching && !searchError && searchQuery.length >= 2 && filteredResults.length === 0 && (
              <div className="mt-2 text-center py-4 text-gray-500 text-sm">
                No users found matching "{searchQuery}"
              </div>
            )}

            {!isSearching && !searchError && searchQuery.length < 2 && searchQuery.length > 0 && (
              <div className="mt-2 text-center py-4 text-gray-500 text-sm">
                Type at least 2 characters to search
              </div>
            )}

            {filteredResults.length > 0 && (
              <div className="mt-2 border border-gray-200 rounded-md max-h-40 overflow-y-auto">
                {filteredResults.map((user) => (
                  <div
                    key={user.id}
                    className="flex items-center justify-between p-3 hover:bg-gray-50 cursor-pointer"
                    onClick={() => handleAddMember(user)}
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                        {user.profile_picture ? (
                          <img
                            src={user.profile_picture}
                            alt={user.full_name}
                            className="w-8 h-8 rounded-full object-cover"
                          />
                        ) : (
                          <span className="text-gray-600 text-sm font-medium">
                            {user.first_name?.[0] || '?'}
                          </span>
                        )}
                      </div>
                      <div>
                        <p className="font-medium text-gray-900 text-sm">
                          {user.full_name}
                        </p>
                        <p className="text-xs text-gray-500">@{user.username}</p>
                      </div>
                    </div>
                    <Button size="sm" variant="outline">
                      Add
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Selected Members */}
          {selectedMembers.length > 0 && (
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Selected Members ({selectedMembers.length})
              </label>
              <div className="space-y-2">
                {selectedMembers.map((member) => (
                  <div
                    key={member.id}
                    className="flex items-center justify-between p-2 bg-blue-50 rounded-md"
                  >
                    <div className="flex items-center space-x-2">
                      <div className="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center">
                        {member.profile_picture ? (
                          <img
                            src={member.profile_picture}
                            alt={member.full_name}
                            className="w-6 h-6 rounded-full object-cover"
                          />
                        ) : (
                          <span className="text-gray-600 text-xs font-medium">
                            {member.first_name[0]}
                          </span>
                        )}
                      </div>
                      <span className="text-sm font-medium text-gray-900">
                        {member.full_name}
                      </span>
                    </div>
                    <button
                      onClick={() => handleRemoveMember(member.id)}
                      disabled={loading}
                      className="text-red-500 hover:text-red-700 transition-colors disabled:opacity-50"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t bg-gray-50">
          <Button variant="outline" onClick={handleClose} disabled={loading}>
            Cancel
          </Button>
          <Button
            onClick={handleCreateGroup}
            disabled={loading || !groupName.trim()}
            loading={loading}
          >
            Create Group
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CreateGroupDialog;
