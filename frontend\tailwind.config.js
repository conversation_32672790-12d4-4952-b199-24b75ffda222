import forms from '@tailwindcss/forms';

/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      // Font Families
      fontFamily: {
        'primary': ['Inter', 'system-ui', 'Avenir', 'Helvetica', 'Arial', 'sans-serif'],
        'secondary': ['Poppins', 'Inter', 'system-ui', 'sans-serif'],
      },

      // Colors using CSS custom properties
      colors: {
        primary: {
          DEFAULT: 'var(--color-primary)',
          hover: 'var(--color-primary-hover)',
          light: 'var(--color-primary-light)',
        },
        secondary: {
          DEFAULT: 'var(--color-secondary)',
          hover: 'var(--color-secondary-hover)',
        },
        danger: {
          DEFAULT: 'var(--color-danger)',
          hover: 'var(--color-danger-hover)',
        },
        success: 'var(--color-success)',
        warning: 'var(--color-warning)',

        // Background colors
        'bg-primary': 'var(--bg-primary)',
        'bg-secondary': 'var(--bg-secondary)',
        'bg-tertiary': 'var(--bg-tertiary)',
        'bg-glass': 'var(--bg-glass)',
        'bg-glass-light': 'var(--bg-glass-light)',
        'bg-glass-heavy': 'var(--bg-glass-heavy)',
        'bg-overlay': 'var(--bg-overlay)',

        // Text colors
        'text-primary': 'var(--text-primary)',
        'text-secondary': 'var(--text-secondary)',
        'text-tertiary': 'var(--text-tertiary)',
        'text-inverse': 'var(--text-inverse)',
        'text-muted': 'var(--text-muted)',

        // Border colors
        'border-primary': 'var(--border-primary)',
        'border-secondary': 'var(--border-secondary)',
        'border-glass': 'var(--border-glass)',
        'border-focus': 'var(--border-focus)',
      },

      // Spacing using CSS custom properties
      spacing: {
        'xs': 'var(--spacing-xs)',
        'sm': 'var(--spacing-sm)',
        'md': 'var(--spacing-md)',
        'lg': 'var(--spacing-lg)',
        'xl': 'var(--spacing-xl)',
        '2xl': 'var(--spacing-2xl)',
        '3xl': 'var(--spacing-3xl)',
      },

      // Border radius using CSS custom properties
      borderRadius: {
        'sm': 'var(--radius-sm)',
        'md': 'var(--radius-md)',
        'lg': 'var(--radius-lg)',
        'xl': 'var(--radius-xl)',
        'full': 'var(--radius-full)',
      },

      // Box shadows using CSS custom properties
      boxShadow: {
        'sm': 'var(--shadow-sm)',
        'md': 'var(--shadow-md)',
        'lg': 'var(--shadow-lg)',
        'xl': 'var(--shadow-xl)',
        'glass': 'var(--shadow-glass)',
      },

      // Background images for gradients
      backgroundImage: {
        'gradient-primary': 'var(--gradient-primary)',
        'gradient-secondary': 'var(--gradient-secondary)',
        'gradient-glass': 'var(--gradient-glass)',
      },

      // Backdrop blur values
      backdropBlur: {
        'xs': '4px',
        'sm': 'var(--glass-blur-sm)',
        'md': 'var(--glass-blur)',
        'lg': 'var(--glass-blur-lg)',
        'xl': '20px',
      },

      // Animation durations
      transitionDuration: {
        '250': '250ms',
        '350': '350ms',
      },

      // Z-index scale
      zIndex: {
        '60': '60',
        '70': '70',
        '80': '80',
        '90': '90',
        '100': '100',
      },
    },
  },
  plugins: [
    forms,
    // Custom plugin for glassmorphism utilities
    function({ addUtilities }) {
      const newUtilities = {
        '.glass': {
          background: 'var(--bg-glass)',
          backdropFilter: 'blur(var(--glass-blur))',
          WebkitBackdropFilter: 'blur(var(--glass-blur))',
          border: '1px solid var(--border-glass)',
        },
        '.glass-light': {
          background: 'var(--bg-glass-light)',
          backdropFilter: 'blur(var(--glass-blur-sm))',
          WebkitBackdropFilter: 'blur(var(--glass-blur-sm))',
          border: '1px solid var(--border-glass)',
        },
        '.glass-heavy': {
          background: 'var(--bg-glass-heavy)',
          backdropFilter: 'blur(var(--glass-blur-lg))',
          WebkitBackdropFilter: 'blur(var(--glass-blur-lg))',
          border: '1px solid var(--border-glass)',
        },
        '.text-gradient': {
          background: 'var(--gradient-primary)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text',
        },
      }
      addUtilities(newUtilities)
    }
  ],
}
