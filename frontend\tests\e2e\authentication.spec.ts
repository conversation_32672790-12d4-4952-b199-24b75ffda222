import { test, expect } from '@playwright/test';

test.describe('Authentication Flow Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Clear any existing authentication state
    await page.context().clearCookies();
    // Navigate to a page first to ensure localStorage is accessible
    await page.goto('/');
    await page.evaluate(() => {
      try {
        localStorage.clear();
        sessionStorage.clear();
      } catch (e) {
        // Ignore localStorage errors in test environment
      }
    });
  });

  test.describe('Login Page', () => {
    test('should display login form with all required elements', async ({ page }) => {
      await page.goto('/login');

      // Check page title and heading (updated to match actual title)
      await expect(page).toHaveTitle(/Vite \+ React \+ TS/);
      await expect(page.getByRole('heading', { name: 'Sign in to your account' })).toBeVisible();

      // Check form elements
      await expect(page.getByPlaceholder('Enter your email address')).toBeVisible();
      await expect(page.getByPlaceholder('Enter your password')).toBeVisible();
      await expect(page.getByRole('button', { name: 'Sign in' })).toBeVisible();

      // Check navigation links
      await expect(page.getByRole('link', { name: 'Sign up' })).toBeVisible();
      await expect(page.getByRole('link', { name: 'Forgot password?' })).toBeVisible();
    });

    test('should show validation errors for empty fields', async ({ page }) => {
      await page.goto('/login');

      // Try to submit empty form
      await page.getByRole('button', { name: 'Sign in' }).click();

      // Check for validation errors (use first occurrence)
      await expect(page.getByText('Email is required').first()).toBeVisible();
      await expect(page.getByText('Password is required').first()).toBeVisible();
    });

    test('should show validation error for invalid email format', async ({ page }) => {
      await page.goto('/login');
      
      // Enter invalid email
      await page.getByTestId('email-input').fill('invalid-email');
      await page.getByTestId('password-input').fill('password123');
      await page.getByTestId('login-button').click();
      
      // Check for email validation error
      await expect(page.getByText('Please enter a valid email address')).toBeVisible();
    });

    test('should show validation error for short password', async ({ page }) => {
      await page.goto('/login');
      
      // Enter short password
      await page.getByTestId('email-input').fill('<EMAIL>');
      await page.getByTestId('password-input').fill('123');
      await page.getByTestId('login-button').click();
      
      // Check for password validation error
      await expect(page.getByText('Password must be at least 8 characters')).toBeVisible();
    });

    test('should toggle password visibility', async ({ page }) => {
      await page.goto('/login');
      
      const passwordInput = page.getByTestId('password-input');
      const toggleButton = page.getByTestId('show-password-button');
      
      // Initially password should be hidden
      await expect(passwordInput).toHaveAttribute('type', 'password');
      
      // Click toggle button to show password
      await toggleButton.click();
      await expect(passwordInput).toHaveAttribute('type', 'text');
      
      // Click again to hide password
      await toggleButton.click();
      await expect(passwordInput).toHaveAttribute('type', 'password');
    });

    test('should show loading state during login attempt', async ({ page }) => {
      await page.goto('/login');
      
      // Fill form with valid data
      await page.getByTestId('email-input').fill('<EMAIL>');
      await page.getByTestId('password-input').fill('password123');
      
      // Mock slow API response
      await page.route('**/api/auth/login/', async route => {
        await new Promise(resolve => setTimeout(resolve, 1000));
        await route.fulfill({
          status: 401,
          contentType: 'application/json',
          body: JSON.stringify({ success: false, error: 'Invalid credentials' })
        });
      });
      
      // Submit form and check loading state
      await page.getByTestId('login-button').click();
      await expect(page.getByTestId('login-button')).toBeDisabled();
      
      // Wait for response and check error message
      await expect(page.getByText('Invalid credentials')).toBeVisible();
    });

    test('should navigate to register page when clicking sign up link', async ({ page }) => {
      await page.goto('/login');
      
      await page.getByRole('link', { name: 'Sign up' }).click();
      await expect(page).toHaveURL('/register');
    });
  });

  test.describe('Register Page', () => {
    test('should display register form with all required elements', async ({ page }) => {
      await page.goto('/register');

      // Check page title and heading (updated to match actual title)
      await expect(page).toHaveTitle(/Vite \+ React \+ TS/);
      await expect(page.getByRole('heading', { name: 'Create your account' })).toBeVisible();

      // Check form elements for email step
      await expect(page.getByPlaceholder('Enter your email address')).toBeVisible();
      await expect(page.getByPlaceholder('Enter your full name')).toBeVisible();
      await expect(page.getByRole('button', { name: 'Send Verification Code' })).toBeVisible();

      // Check navigation links
      await expect(page.getByRole('link', { name: 'Sign in' })).toBeVisible();
    });

    test('should show validation errors for empty fields', async ({ page }) => {
      await page.goto('/register');

      // Try to submit empty form
      await page.getByRole('button', { name: 'Send Verification Code' }).click();

      // Check for validation errors (use first occurrence)
      await expect(page.getByText('Email is required').first()).toBeVisible();
      await expect(page.getByText('Name is required').first()).toBeVisible();
    });

    test('should validate email format', async ({ page }) => {
      await page.goto('/register');
      
      await page.getByTestId('email-input').fill('invalid-email');
      await page.getByTestId('register-button').click();
      
      await expect(page.getByText('Please enter a valid email address')).toBeVisible();
    });

    test('should validate username requirements', async ({ page }) => {
      await page.goto('/register');
      
      // Test short username
      await page.getByTestId('username-input').fill('ab');
      await page.getByTestId('register-button').click();
      await expect(page.getByText('Username must be at least 3 characters')).toBeVisible();
      
      // Test invalid characters
      await page.getByTestId('username-input').fill('user@name');
      await page.getByTestId('register-button').click();
      await expect(page.getByText('Username can only contain letters, numbers, and underscores')).toBeVisible();
    });

    test('should validate name requirements', async ({ page }) => {
      await page.goto('/register');
      
      // Test short first name
      await page.getByTestId('firstName-input').fill('A');
      await page.getByTestId('register-button').click();
      await expect(page.getByText('First name must be at least 2 characters')).toBeVisible();
      
      // Test invalid characters in first name
      await page.getByTestId('firstName-input').fill('John123');
      await page.getByTestId('register-button').click();
      await expect(page.getByText('First name can only contain letters and spaces')).toBeVisible();
    });

    test('should validate password requirements', async ({ page }) => {
      await page.goto('/register');
      
      // Test short password
      await page.getByTestId('password-input').fill('123');
      await page.getByTestId('register-button').click();
      await expect(page.getByText('Password must be at least 8 characters')).toBeVisible();
      
      // Test password without required characters
      await page.getByTestId('password-input').fill('password');
      await page.getByTestId('register-button').click();
      await expect(page.getByText('Password must contain at least one uppercase letter, one lowercase letter, and one number')).toBeVisible();
    });

    test('should validate password confirmation', async ({ page }) => {
      await page.goto('/register');
      
      await page.getByTestId('password-input').fill('Password123');
      await page.getByTestId('confirmPassword-input').fill('Password456');
      await page.getByTestId('register-button').click();
      
      await expect(page.getByText('Passwords must match')).toBeVisible();
    });

    test('should toggle password visibility for both password fields', async ({ page }) => {
      await page.goto('/register');
      
      const passwordInput = page.getByTestId('password-input');
      const confirmPasswordInput = page.getByTestId('confirmPassword-input');
      const passwordToggle = page.getByTestId('show-password-button');
      const confirmPasswordToggle = page.getByTestId('show-confirm-password-button');
      
      // Initially both passwords should be hidden
      await expect(passwordInput).toHaveAttribute('type', 'password');
      await expect(confirmPasswordInput).toHaveAttribute('type', 'password');
      
      // Toggle password visibility
      await passwordToggle.click();
      await expect(passwordInput).toHaveAttribute('type', 'text');
      
      await confirmPasswordToggle.click();
      await expect(confirmPasswordInput).toHaveAttribute('type', 'text');
    });

    test('should navigate to login page when clicking sign in link', async ({ page }) => {
      await page.goto('/register');
      
      await page.getByRole('link', { name: 'Sign in' }).click();
      await expect(page).toHaveURL('/login');
    });
  });

  test.describe('Navigation Between Pages', () => {
    test('should navigate between login and register pages', async ({ page }) => {
      // Start at login
      await page.goto('/login');
      await expect(page.getByRole('heading', { name: 'Sign in to your account' })).toBeVisible();

      // Go to register
      await page.getByRole('link', { name: 'Sign up' }).click();
      await expect(page).toHaveURL('/register');
      await expect(page.getByRole('heading', { name: 'Create your account' })).toBeVisible();

      // Go back to login
      await page.getByRole('link', { name: 'Sign in' }).click();
      await expect(page).toHaveURL('/login');
      await expect(page.getByRole('heading', { name: 'Sign in to your account' })).toBeVisible();
    });
  });

  test.describe('Error Handling', () => {
    test('should handle login API errors gracefully', async ({ page }) => {
      await page.goto('/login');

      // Mock API error response
      await page.route('**/api/auth/login/', async route => {
        await route.fulfill({
          status: 401,
          contentType: 'application/json',
          body: JSON.stringify({
            success: false,
            error: 'Invalid email or password'
          })
        });
      });

      // Fill and submit form
      await page.getByTestId('email-input').fill('<EMAIL>');
      await page.getByTestId('password-input').fill('wrongpassword');
      await page.getByTestId('login-button').click();

      // Check error message is displayed
      await expect(page.getByText('Invalid email or password')).toBeVisible();
      await expect(page.getByText('Login Error')).toBeVisible();
    });

    test('should handle register API errors gracefully', async ({ page }) => {
      await page.goto('/register');

      // Mock API error response
      await page.route('**/api/auth/register/', async route => {
        await route.fulfill({
          status: 400,
          contentType: 'application/json',
          body: JSON.stringify({
            success: false,
            error: 'Email already exists'
          })
        });
      });

      // Fill and submit form
      await page.getByTestId('email-input').fill('<EMAIL>');
      await page.getByTestId('username-input').fill('testuser');
      await page.getByTestId('firstName-input').fill('Test');
      await page.getByTestId('lastName-input').fill('User');
      await page.getByTestId('password-input').fill('Password123');
      await page.getByTestId('confirmPassword-input').fill('Password123');
      await page.getByTestId('register-button').click();

      // Check that form shows error (Formik will handle the error display)
      await expect(page.getByTestId('register-button')).not.toBeDisabled();
    });

    test('should handle network errors', async ({ page }) => {
      await page.goto('/login');

      // Mock network error
      await page.route('**/api/auth/login/', async route => {
        await route.abort('failed');
      });

      // Fill and submit form
      await page.getByTestId('email-input').fill('<EMAIL>');
      await page.getByTestId('password-input').fill('password123');
      await page.getByTestId('login-button').click();

      // Should show generic error message
      await expect(page.getByText('Login failed')).toBeVisible();
    });
  });

  test.describe('Success Scenarios', () => {
    test('should successfully login and redirect to chat', async ({ page }) => {
      await page.goto('/login');

      // Mock successful login response
      await page.route('**/api/auth/login/', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: {
              user: {
                id: '1',
                email: '<EMAIL>',
                username: 'testuser',
                firstName: 'Test',
                lastName: 'User'
              },
              tokens: {
                access: 'mock-access-token',
                refresh: 'mock-refresh-token'
              }
            }
          })
        });
      });

      // Fill and submit form
      await page.getByTestId('email-input').fill('<EMAIL>');
      await page.getByTestId('password-input').fill('password123');
      await page.getByTestId('login-button').click();

      // Should redirect to chat page
      await expect(page).toHaveURL('/chat');

      // Verify tokens are stored
      const accessToken = await page.evaluate(() => localStorage.getItem('token'));
      const refreshToken = await page.evaluate(() => localStorage.getItem('refreshToken'));
      expect(accessToken).toBe('mock-access-token');
      expect(refreshToken).toBe('mock-refresh-token');
    });

    test('should successfully register and redirect to chat', async ({ page }) => {
      await page.goto('/register');

      // Mock successful register response
      await page.route('**/api/auth/register/', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: {
              user: {
                id: '2',
                email: '<EMAIL>',
                username: 'newuser',
                firstName: 'New',
                lastName: 'User'
              },
              tokens: {
                access: 'mock-access-token-2',
                refresh: 'mock-refresh-token-2'
              }
            }
          })
        });
      });

      // Fill and submit form
      await page.getByTestId('email-input').fill('<EMAIL>');
      await page.getByTestId('username-input').fill('newuser');
      await page.getByTestId('firstName-input').fill('New');
      await page.getByTestId('lastName-input').fill('User');
      await page.getByTestId('password-input').fill('Password123');
      await page.getByTestId('confirmPassword-input').fill('Password123');
      await page.getByTestId('register-button').click();

      // Should redirect to chat page
      await expect(page).toHaveURL('/chat');

      // Verify tokens are stored
      const accessToken = await page.evaluate(() => localStorage.getItem('token'));
      const refreshToken = await page.evaluate(() => localStorage.getItem('refreshToken'));
      expect(accessToken).toBe('mock-access-token-2');
      expect(refreshToken).toBe('mock-refresh-token-2');
    });
  });
});
