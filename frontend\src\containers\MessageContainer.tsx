// frontend/src/containers/MessageContainer.tsx
import React, { useEffect, useRef, useMemo, useCallback, useState } from 'react';
import { useSelector } from 'react-redux';
import { List, useListRef } from "react-window";
import type { RowComponentProps } from "react-window";
import { useSocket } from '../contexts/SocketContext';
import { useAuth } from '../contexts/AuthContext';
import { useGetMessagesQuery } from '../services';
import { useMessageDecryption } from '../hooks/useMessageDecryption';
import {
  MessageWrapper,
  TextMessage,
  ImageMessage,
  AudioMessage,
  FileMessage,
  GroupUpdateMessage,
  CallingMessage
} from '../components/ui/messages';
import { Button, Icon, MessageInput } from '../components/ui';
import {
  selectSortedMessagesByConversation,
  selectSendingMessages,
  selectMessageStatuses,
  selectFailedMessages,
  selectTypingUsersByConversation,
  type RootState
} from '../store';
import type { Message as ApiMessage } from '../services/messageApi';
import type { Message as StoreMessage } from '../store/slices/messageSlice';

// Union type to handle both API and store message formats
type MessageType = ApiMessage | StoreMessage;

/**
 * Helper functions to normalize message properties across different formats
 */
const getMessageTimestamp = (message: MessageType): string => {
  // Both API and Store messages should have createdAt
  return (message as any).createdAt || (message as any).created_at;
};

const getMessageSenderId = (message: MessageType): string => {
  return message.sender.id;
};

const getMessageType = (message: MessageType): string => {
  // Both API and Store messages should have messageType
  return (message as any).messageType || (message as any).message_type;
};

const getSenderName = (sender: MessageType['sender']): string => {
  const firstName = sender.first_name || '';
  const lastName = sender.last_name || '';
  return `${firstName} ${lastName}`.trim() || sender.username;
};

const getSenderAvatar = (sender: MessageType['sender']): string | undefined => {
  return sender.profile_picture;
};

/**
 * Props for the MessageContainer component
 */
export interface MessageContainerProps {
  /** Conversation ID */
  conversationId: string;
  /** Custom class name */
  className?: string;
  /** Reply handler */
  onReply?: (message: MessageType) => void;
}

/**
 * Message item height for virtualization
 */
const MESSAGE_ITEM_HEIGHT = 80;

/**
 * Component that handles message decryption and rendering
 */
const DecryptedMessage: React.FC<{
  message: MessageType;
  isOwn: boolean;
  sender: any;
  timestamp: Date;
  status: string;
  showAvatar: boolean;
  showTimestamp: boolean;
  isGrouped: boolean;
  isFirstInGroup: boolean;
  isLastInGroup: boolean;
  onReply?: (message: MessageType) => void;
}> = ({
  message,
  isOwn,
  sender,
  timestamp,
  status,
  showAvatar,
  showTimestamp,
  isGrouped,
  isFirstInGroup,
  isLastInGroup,
  onReply
}) => {
  const { decryptedContent, isDecrypting, decryptionError } = useMessageDecryption(message);
  const messageType = getMessageType(message);

  console.log('🔓 [DECRYPTED_MESSAGE] Message decryption result:', {
    messageId: message.id,
    decryptedContentLength: decryptedContent?.length || 0,
    isDecrypting,
    decryptionError,
    messageType,
    hasContent: !!message.content,
    hasEncryptedContent: !!(message as any).encryptedContent,
    isEncrypted: !!(message as any).isEncrypted
  });

  const renderMessageContent = () => {
    if (isDecrypting) {
      return <div className="text-gray-500 italic">Decrypting...</div>;
    }

    if (decryptionError) {
      return <div className="text-red-500">Decryption failed: {decryptionError}</div>;
    }

    if (!decryptedContent) {
      return <div className="text-gray-500 italic">No content</div>;
    }

    switch (messageType) {
      case 'TEXT':
        return (
          <TextMessage
            content={decryptedContent}
            isOwn={isOwn}
            enableLinkDetection
          />
        );
      default:
        return (
          <TextMessage
            content={decryptedContent}
            isOwn={isOwn}
            enableLinkDetection
          />
        );
    }
  };

  return (
    <MessageWrapper
      messageId={message.id}
      isOwn={isOwn}
      sender={sender}
      timestamp={timestamp}
      status={status}
      showAvatar={showAvatar}
      showTimestamp={showTimestamp}
      isGrouped={isGrouped}
      isFirstInGroup={isFirstInGroup}
      isLastInGroup={isLastInGroup}
      className="mb-2"
      onReply={() => onReply?.(message)}
    >
      {renderMessageContent()}
    </MessageWrapper>
  );
};


/**
 * Enhanced MessageContainer component with virtualization and comprehensive message handling
 *
 * Features:
 * - Message virtualization for performance with large lists using react-window
 * - Real-time message updates via Socket.IO
 * - Support for all message types (text, image, audio, file, system)
 * - Message status indicators and retry functionality
 * - Typing indicators
 * - Auto-scroll to bottom for new messages
 * - Draft conversation support
 * - Connection status handling
 * - Separate MessageInput component integration
 *
 * Replaces: ChatRoom.tsx and MessageList.tsx
 */
export const MessageContainer: React.FC<MessageContainerProps> = ({
  conversationId,
  className = '',
  onReply,
}) => {
  const { user } = useAuth();
  const { joinConversation, isConnected } = useSocket();
  const listRef = useListRef(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const [shouldAutoScroll, setShouldAutoScroll] = useState(true);
  const [replyTo, setReplyTo] = useState<{
    id: string;
    content: string;
    sender: string;
  } | null>(null);
  const [listHeight, setListHeight] = useState(400);

  // Check if this is a draft conversation
  const isDraftConversation = conversationId?.startsWith('draft-');

  // Use RTK Query to get messages - skip for draft conversations
  const { data: messagesData, isLoading, error, refetch } = useGetMessagesQuery(
    { conversationId },
    { skip: !conversationId || isDraftConversation }
  );

  // Effect to refetch messages when a draft conversation becomes a real conversation
  useEffect(() => {
    // If the conversation ID changed from a draft to a real conversation, refetch messages
    if (conversationId && !isDraftConversation && messagesData === undefined && !isLoading && !error) {
      console.log('🔄 [MESSAGE_CONTAINER] Refetching messages for converted conversation:', conversationId);
      refetch();
    }
  }, [conversationId, isDraftConversation, messagesData, isLoading, error, refetch]);

  // Use memoized selectors for Redux store data (real-time features)
  const sendingMessagesMap = useSelector(selectSendingMessages);
  const messageStatuses = useSelector(selectMessageStatuses);
  const failedMessagesMap = useSelector(selectFailedMessages);
  const reduxMessages = useSelector((state: RootState) =>
    selectSortedMessagesByConversation(state, conversationId)
  );
  const typingUsers = useSelector((state: RootState) =>
    selectTypingUsersByConversation(state, conversationId)
  );

  // Convert objects to arrays for easier checking
  const sendingMessages = Object.keys(sendingMessagesMap).filter(key => sendingMessagesMap[key]);
  const failedMessages = Object.keys(failedMessagesMap).filter(key => failedMessagesMap[key]);

  // Combine API messages with real-time Redux messages
  const allMessages = useMemo(() => {
    const apiMessages = messagesData?.results || [];
    const combinedMessages = [...apiMessages, ...reduxMessages];

    console.log('🔄 [MESSAGE_CONTAINER] Message combination debug:', {
      conversationId,
      isDraftConversation,
      apiMessagesCount: apiMessages.length,
      reduxMessagesCount: reduxMessages.length,
      combinedCount: combinedMessages.length,
      messagesDataExists: !!messagesData,
      messagesDataResults: messagesData?.results?.length || 0
    });

    // Remove duplicates based on message ID
    const uniqueMessages = combinedMessages.reduce((acc, message) => {
      if (!acc.find(m => m.id === message.id)) {
        acc.push(message);
      }
      return acc;
    }, [] as MessageType[]);

    // Sort by timestamp
    const sortedMessages = uniqueMessages.sort((a, b) =>
      new Date(getMessageTimestamp(a)).getTime() - new Date(getMessageTimestamp(b)).getTime()
    );

    console.log('🔄 [MESSAGE_CONTAINER] Final messages:', {
      uniqueCount: uniqueMessages.length,
      sortedCount: sortedMessages.length,
      messageIds: sortedMessages.map(m => m.id)
    });

    return sortedMessages;
  }, [messagesData?.results, reduxMessages, conversationId, isDraftConversation]);

  // Join conversation room for real-time updates
  useEffect(() => {
    if (conversationId && isConnected && !isDraftConversation) {
      joinConversation(conversationId);
    }
  }, [conversationId, joinConversation, isConnected, isDraftConversation]);

  // Dynamic height calculation for react-window
  useEffect(() => {
    const updateHeight = () => {
      if (messagesContainerRef.current) {
        const rect = messagesContainerRef.current.getBoundingClientRect();
        setListHeight(rect.height);
      }
    };

    updateHeight();

    const resizeObserver = new ResizeObserver(updateHeight);
    if (messagesContainerRef.current) {
      resizeObserver.observe(messagesContainerRef.current);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (shouldAutoScroll && allMessages.length > 0) {
      scrollToBottom();
    }
  }, [allMessages.length, shouldAutoScroll]);

  // Scroll to bottom function
  const scrollToBottom = useCallback(() => {
    if (listRef.current) {
      listRef.current.scrollToRow({
        index: allMessages.length - 1,
        align: 'end',
        behavior: 'smooth'
      });
    }
  }, [allMessages.length]);

  // Render individual message item for react-window
  const renderMessageItem = useCallback(({ index, style }: RowComponentProps) => {
    const message = allMessages[index];
    console.log('🔄 [MESSAGE_CONTAINER] Rendering message item:', {
      index,
      messageExists: !!message,
      messageId: message?.id,
      userExists: !!user,
      userId: user?.id
    });

    if (!message || !user) {
      console.log('🔄 [MESSAGE_CONTAINER] ❌ Skipping message render - missing message or user');
      return null;
    }

    const isOwnMessage = getMessageSenderId(message) === user.id;
    const messageId = message.id;
    const isSending = sendingMessages.includes(messageId);
    const messageStatus = messageStatuses[messageId];
    const isFailed = failedMessages.includes(messageId);

    console.log('🔄 [MESSAGE_CONTAINER] Message render details:', {
      messageId,
      isOwnMessage,
      isSending,
      messageStatus,
      isFailed,
      messageType: getMessageType(message),
      senderId: getMessageSenderId(message)
    });

    // Message grouping and display settings
    const showAvatar = true; // Default to showing avatar
    const isGrouped = false; // Default to no grouping for now
    const isFirstInGroup = true; // Default to first in group
    const isLastInGroup = true; // Default to last in group

    // Get sender information
    const sender = message.sender ? {
      id: message.sender.id,
      name: getSenderName(message.sender),
      avatar: getSenderAvatar(message.sender),
    } : undefined;

    // Render message content based on type
    const renderMessageContent = () => {
      const messageType = getMessageType(message);
      const messageContent = message.content;

      switch (messageType) {
        case 'TEXT':
          return (
            <TextMessage
              content={messageContent}
              isOwn={isOwnMessage}
              enableLinkDetection
            />
          );
        case 'IMAGE':
          return (
            <ImageMessage
              src={(message as any).mediaUrl || ''}
              alt="Image message"
              caption={messageContent}
              isOwn={isOwnMessage}
              enableLightbox
              showDownload
            />
          );
        case 'AUDIO':
          return (
            <AudioMessage
              src={(message as any).mediaUrl || ''}
              duration={(message as any).mediaDuration}
              isOwn={isOwnMessage}
              showWaveform
            />
          );
        case 'FILE':
          return (
            <FileMessage
              file={{
                name: (message as any).fileName || 'Unknown file',
                size: (message as any).fileSize || 0,
                type: (message as any).mimeType || 'application/octet-stream',
                url: (message as any).mediaUrl || '',
              }}
              isOwn={isOwnMessage}
              showPreview
            />
          );
        case 'SYSTEM':
          if (messageContent.includes('call')) {
            return (
              <CallingMessage
                callType={messageContent.includes('video') ? 'video' : 'audio'}
                status={messageContent.includes('missed') ? 'missed' : 'ended'}
                duration={(message as any).mediaDuration}
                isOutgoing={isOwnMessage}
              />
            );
          } else {
            return (
              <GroupUpdateMessage
                updateType="member_added" // This should be determined from message content
                content={messageContent}
                timestamp={new Date(getMessageTimestamp(message))}
              />
            );
          }
        default:
          return (
            <TextMessage
              content={messageContent}
              isOwn={isOwnMessage}
              enableLinkDetection
            />
          );
      }
    };

    console.log('🔄 [MESSAGE_CONTAINER] About to render MessageWrapper for:', messageId);

    try {
      const result = (
        <div style={style} className="px-4">
          <MessageWrapper
            key={messageId}
            messageId={messageId}
            isOwn={isOwnMessage}
            sender={sender}
            timestamp={new Date(getMessageTimestamp(message))}
            status={isSending ? 'sending' : messageStatus || 'sent'}
            showAvatar={showAvatar}
            showTimestamp={true}
            isGrouped={isGrouped}
            isFirstInGroup={isFirstInGroup}
            isLastInGroup={isLastInGroup}
            className="mb-2"
            onReply={() => onReply?.(message)}
          >
            {renderMessageContent()}
          </MessageWrapper>
        </div>
      );

      console.log('🔄 [MESSAGE_CONTAINER] ✅ MessageWrapper rendered successfully for:', messageId);
      return result;
    } catch (error) {
      console.error('🔄 [MESSAGE_CONTAINER] ❌ Error rendering MessageWrapper:', error);
      return null;
    }
  }, [allMessages, user, sendingMessages, messageStatuses, failedMessages]);

  // Show loading state
  if (isLoading && !isDraftConversation) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <Icon name="loader" className="animate-spin text-text-secondary mb-2" size={24} />
          <p className="text-text-secondary">Loading messages...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error && !isDraftConversation) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <Icon name="alert-circle" className="text-danger mb-2" size={24} />
          <p className="text-danger">Failed to load messages</p>
          <p className="text-text-secondary text-sm mb-3">
            {error && typeof error === 'object' && 'data' in error
              ? (error.data as any)?.error || 'Network error occurred'
              : 'Network error occurred'
            }
          </p>
          <Button
            variant="outline"
            size="small"
            className="mt-2"
            onClick={() => refetch()}
          >
            Retry
          </Button>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <p className="text-text-secondary">Please log in to access chat</p>
      </div>
    );
  }

  const containerClasses = [
    'flex flex-col h-full bg-bg-primary',
    className,
  ].filter(Boolean).join(' ');

  return (
    <div className={containerClasses}>
      {/* Connection Status */}
      {!isConnected && (
        <div className="bg-warning/10 border-b border-warning/20 px-4 py-2">
          <div className="flex items-center space-x-2 text-warning text-sm">
            <div className="w-2 h-2 bg-warning rounded-full animate-pulse"></div>
            <span>Reconnecting...</span>
          </div>
        </div>
      )}

      {/* Messages List with Virtualization */}
      <div className="flex-1 relative">
        {allMessages.length > 0 ? (
          <>
            {console.log('🔄 [MESSAGE_CONTAINER] Rendering simple div with messages:', {
              messagesCount: allMessages.length,
              listHeight,
              rowHeight: MESSAGE_ITEM_HEIGHT,
              totalHeight: allMessages.length * MESSAGE_ITEM_HEIGHT
            })}
            <div
              style={{ height: listHeight, overflowY: 'auto' }}
              className="scrollbar-thin scrollbar-thumb-border-glass scrollbar-track-transparent"
            >
              {allMessages.map((message, index) => {
                const messageId = message.id;
                const isOwnMessage = getMessageSenderId(message) === user?.id;
                const isSending = sendingMessages.includes(messageId);
                const messageStatus = messageStatuses[messageId];
                const isFailed = failedMessages.includes(messageId);

                // Message grouping and display settings
                const showAvatar = true;
                const isGrouped = false;
                const isFirstInGroup = true;
                const isLastInGroup = true;

                // Get sender information
                const sender = {
                  id: getMessageSenderId(message),
                  name: getSenderName(message.sender),
                  avatar: getSenderAvatar(message.sender),
                };

                // Render message content based on type
                const messageType = getMessageType(message);
                const messageContent = message.content;

                const renderMessageContent = () => {
                  switch (messageType) {
                    case 'TEXT':
                      return (
                        <TextMessage
                          content={messageContent}
                          isOwn={isOwnMessage}
                          enableLinkDetection
                        />
                      );
                    default:
                      return (
                        <TextMessage
                          content={messageContent}
                          isOwn={isOwnMessage}
                          enableLinkDetection
                        />
                      );
                  }
                };

                console.log('🔄 [MESSAGE_CONTAINER] Rendering full message:', {
                  index,
                  messageId,
                  messageType,
                  contentLength: messageContent?.length || 0,
                  isOwnMessage
                });

                return (
                  <div key={messageId} className="px-4 mb-2">
                    <DecryptedMessage
                      message={message}
                      isOwn={isOwnMessage}
                      sender={sender}
                      timestamp={new Date(getMessageTimestamp(message))}
                      status={isSending ? 'sending' : messageStatus || 'sent'}
                      showAvatar={showAvatar}
                      showTimestamp={true}
                      isGrouped={isGrouped}
                      isFirstInGroup={isFirstInGroup}
                      isLastInGroup={isLastInGroup}
                      onReply={onReply}
                    />
                  </div>
                );
              })}
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <Icon name="message-circle" className="text-text-tertiary mb-3" size={32} />
              <h3 className="text-text-secondary font-medium mb-1">No messages yet</h3>
              <p className="text-text-tertiary text-sm">
                Start the conversation by sending a message
              </p>
            </div>
          </div>
        )}

        {/* Typing Indicators */}
        {typingUsers.length > 0 && (
          <div className="absolute bottom-4 left-4 bg-bg-glass rounded-lg px-3 py-2 border border-border-glass">
            <div className="flex items-center space-x-2">
              <div className="flex space-x-1">
                <div className="w-1 h-1 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                <div className="w-1 h-1 bg-primary rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                <div className="w-1 h-1 bg-primary rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
              </div>
              <span className="text-xs text-text-secondary">
                {typingUsers.length === 1
                  ? `${typingUsers[0]} is typing...`
                  : `${typingUsers.length} people are typing...`
                }
              </span>
            </div>
          </div>
        )}

        {/* Scroll to bottom button */}
        {!shouldAutoScroll && (
          <button
            onClick={scrollToBottom}
            className="absolute bottom-4 right-4 bg-primary text-white rounded-full p-2 shadow-lg hover:bg-primary-hover transition-colors"
          >
            <Icon name="arrow-down" size={16} />
          </button>
        )}
      </div>

      {/* Message Input */}
      <MessageInput
        conversationId={conversationId}
        disabled={!isConnected}
        replyTo={replyTo}
        onCancelReply={() => setReplyTo(null)}
      />
    </div>
  );
};

export default MessageContainer;
