// frontend/src/components/Chat/MessageInput.tsx
import React, { useState, useRef } from 'react';
import { useSelector } from 'react-redux';
import { Button } from '../ui/Button';
import { Icon } from '../ui/Icon';
import { useSocket } from '../../contexts/SocketContext';
import SimpleMediaUpload from './SimpleMediaUpload';
import type { RootState } from '../../store';
import type { UploadProgress } from '../../services/mediaApi';
import { useThemeClasses } from '../../hooks/useThemeClasses';

interface MessageInputProps {
  conversationId: string;
  disabled?: boolean;
  replyTo?: {
    id: string;
    content: string;
    sender: string;
  } | null;
  onCancelReply?: () => void;
}

const MessageInput: React.FC<MessageInputProps> = ({
  conversationId,
  disabled = false,
  replyTo,
  onCancelReply,
}) => {
  const [message, setMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [uploadingFiles, setUploadingFiles] = useState<string[]>([]);
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});
  const typingTimeoutRef = useRef<number | undefined>(undefined);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const { sendMessage, startTyping, stopTyping, isConnected } = useSocket();
  const loading = useSelector((state: RootState) => state.messages.loading);
  const { isDark } = useThemeClasses();

  const handleInputChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = event.target.value;
    setMessage(value);

    // Auto-resize textarea
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 120)}px`;
    }

    // Handle typing indicators
    if (value && !isTyping && isConnected) {
      setIsTyping(true);
      startTyping(conversationId);
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout to stop typing indicator
    typingTimeoutRef.current = window.setTimeout(() => {
      if (isTyping && isConnected) {
        setIsTyping(false);
        stopTyping(conversationId);
      }
    }, 1000);
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (message.trim() && !disabled && isConnected) {
      // Send message through SocketContext with optimistic update
      await sendMessage(conversationId, message.trim());

      setMessage('');

      // Reset textarea height
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }

      // Stop typing indicator
      if (isTyping) {
        setIsTyping(false);
        stopTyping(conversationId);
      }

      // Clear timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    }
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSubmit(event);
    }
  };

  // Media upload handlers
  const handleUploadStart = (file: File) => {
    const fileId = `${file.name}_${Date.now()}`;
    setUploadingFiles(prev => [...prev, fileId]);
    setUploadProgress(prev => ({ ...prev, [fileId]: 0 }));
  };

  const handleUploadProgress = (progress: UploadProgress) => {
    // Update progress for the current file
    // Note: In a real implementation, you'd need to track which file this progress belongs to
    setUploadProgress(prev => {
      const fileId = Object.keys(prev)[Object.keys(prev).length - 1];
      return { ...prev, [fileId]: progress.percentage };
    });
  };

  const handleUploadComplete = async (uploadResults: any[]) => {
    // Remove from uploading files
    setUploadingFiles(prev => prev.slice(0, -1));

    // Clear progress
    setUploadProgress(prev => {
      const newProgress = { ...prev };
      const fileId = Object.keys(newProgress)[Object.keys(newProgress).length - 1];
      delete newProgress[fileId];
      return newProgress;
    });

    console.log('Media upload completed:', uploadResults);
    
    // The backend auto-creates messages for media uploads
    // The socket system will automatically receive these messages through the SocketContext
    setTimeout(() => {
      console.log('✅ Media upload integration completed');
    }, 100);
  };

  const handleUploadError = (error: string) => {
    console.error('Media upload error:', error);

    // Remove from uploading files
    setUploadingFiles(prev => prev.slice(0, -1));

    // Clear progress
    setUploadProgress(prev => {
      const newProgress = { ...prev };
      const fileId = Object.keys(newProgress)[Object.keys(newProgress).length - 1];
      delete newProgress[fileId];
      return newProgress;
    });
  };

  return (
    <div className={`p-4 border-t ${isDark ? 'border-gray-700' : 'border-gray-200'}`}>
      {/* Connection status indicator */}
      {!isConnected && (
        <div className={`mb-3 p-2 rounded-lg border ${
          isDark
            ? 'bg-yellow-900/20 border-yellow-700'
            : 'bg-yellow-100 border-yellow-300'
        }`}>
          <div className={`flex items-center space-x-2 text-sm ${
            isDark ? 'text-yellow-400' : 'text-yellow-700'
          }`}>
            <Icon name="alert-triangle" size={16} />
            <span>Reconnecting to chat server...</span>
          </div>
        </div>
      )}

      {/* Reply indicator */}
      {replyTo && (
        <div className={`mb-3 p-3 rounded-lg border-l-4 border-teal-500 ${
          isDark ? 'bg-gray-800' : 'bg-gray-100'
        }`}>
          <div className="flex items-center justify-between">
            <div>
              <div className={`text-sm font-medium ${
                isDark ? 'text-white' : 'text-gray-900'
              }`}>
                Replying to {replyTo.sender}
              </div>
              <div className={`text-sm truncate ${
                isDark ? 'text-gray-400' : 'text-gray-600'
              }`}>
                {replyTo.content}
              </div>
            </div>
            {onCancelReply && (
              <button
                onClick={onCancelReply}
                className={`transition-colors ${
                  isDark
                    ? 'text-gray-400 hover:text-gray-300'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                <Icon name="x" size={16} />
              </button>
            )}
          </div>
        </div>
      )}

      <div className="flex items-center space-x-3">
        {/* Emoji button */}
        <button className={`w-10 h-10 flex items-center justify-center rounded-full transition-colors ${
          isDark
            ? 'text-gray-400 hover:text-white hover:bg-gray-700'
            : 'text-gray-500 hover:text-gray-900 hover:bg-gray-100'
        }`}>
          <Icon name="smile" size={20} />
        </button>

        {/* Message input */}
        <div className="flex-1 relative">
          <input
            ref={textareaRef as any}
            value={message}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            placeholder={isConnected ? "Message to User..." : "Connecting..."}
            disabled={disabled || loading || !isConnected}
            className={`w-full px-4 py-3 rounded-full focus:outline-none focus:ring-2 focus:ring-teal-500 ${
              isDark
                ? 'bg-gray-700 text-white placeholder-gray-400'
                : 'bg-gray-100 text-gray-900 placeholder-gray-500'
            }`}
            data-testid="message-input"
          />
          <button className={`absolute right-3 top-1/2 transform -translate-y-1/2 transition-colors ${
            isDark
              ? 'text-gray-400 hover:text-white'
              : 'text-gray-500 hover:text-gray-700'
          }`}>
            <Icon name="paperclip" size={20} />
          </button>
        </div>

        {/* Send button */}
        <button
          type="submit"
          disabled={!message.trim() || disabled || loading || !isConnected}
          className="w-10 h-10 flex items-center justify-center bg-teal-500 text-white rounded-full hover:bg-teal-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          data-testid="send-button"
        >
          <Icon name="send" size={20} />
        </button>
      </div>
    </div>
  );
};

export default MessageInput;
