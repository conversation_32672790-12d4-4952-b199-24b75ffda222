// frontend/src/components/ui/messages/TextMessage.tsx
import React, { useMemo } from 'react';
import { But<PERSON> } from '../Button';
import { useThemeClasses } from '../../../hooks/useThemeClasses';

/**
 * Props for the TextMessage component
 */
export interface TextMessageProps {
  /** The text content of the message */
  content: string;
  /** Whether this message is from the current user */
  isOwn?: boolean;
  /** Whether the message is being edited */
  isEditing?: boolean;
  /** Custom class name */
  className?: string;
  /** Click handler for the message */
  onClick?: () => void;
  /** Whether to enable link detection and formatting */
  enableLinkDetection?: boolean;
  /** Whether to enable emoji rendering */
  enableEmoji?: boolean;
  /** Maximum length before truncation */
  maxLength?: number;
  /** Whether the message is truncated */
  isTruncated?: boolean;
  /** Handler for expanding truncated message */
  onExpand?: () => void;
}

/**
 * Link detection regex pattern
 */
const URL_REGEX = /(https?:\/\/[^\s]+)/g;

/**
 * Email detection regex pattern
 */
const EMAIL_REGEX = /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g;

/**
 * Enhanced TextMessage component for displaying plain text messages
 * 
 * Features:
 * - Automatic link detection and formatting
 * - Email detection and mailto links
 * - Text truncation with expand functionality
 * - Emoji support
 * - Copy-to-clipboard functionality
 * - Glassmorphism design system integration
 * - Responsive design
 * 
 * @example
 * ```tsx
 * <TextMessage 
 *   content="Check out this link: https://example.com"
 *   isOwn={false}
 *   enableLinkDetection
 *   maxLength={200}
 * />
 * ```
 */
export const TextMessage: React.FC<TextMessageProps> = ({
  content,
  isOwn = false,
  isEditing = false,
  className = '',
  onClick,
  enableLinkDetection = true,
  enableEmoji = true,
  maxLength,
  isTruncated = false,
  onExpand,
}) => {
  const { classes, isDark } = useThemeClasses();
  // Process text content with link detection
  const processedContent = useMemo(() => {
    if (!enableLinkDetection) {
      return content;
    }

    let processedText = content;
    const elements: (string | React.ReactElement)[] = [];
    let lastIndex = 0;

    // Find all URLs and emails
    const urlMatches = Array.from(content.matchAll(URL_REGEX));
    const emailMatches = Array.from(content.matchAll(EMAIL_REGEX));
    
    // Combine and sort matches by index
    const allMatches = [...urlMatches, ...emailMatches].sort((a, b) => a.index! - b.index!);

    allMatches.forEach((match, index) => {
      const matchStart = match.index!;
      const matchEnd = matchStart + match[0].length;

      // Add text before the match
      if (matchStart > lastIndex) {
        elements.push(content.slice(lastIndex, matchStart));
      }

      // Add the link
      const isEmail = match[0].includes('@');
      const href = isEmail ? `mailto:${match[0]}` : match[0];
      
      elements.push(
        <a
          key={`link-${index}`}
          href={href}
          target={isEmail ? undefined : '_blank'}
          rel={isEmail ? undefined : 'noopener noreferrer'}
          className="text-primary hover:text-primary-hover underline transition-colors"
          onClick={(e) => e.stopPropagation()}
        >
          {match[0]}
        </a>
      );

      lastIndex = matchEnd;
    });

    // Add remaining text
    if (lastIndex < content.length) {
      elements.push(content.slice(lastIndex));
    }

    return elements.length > 0 ? elements : content;
  }, [content, enableLinkDetection]);

  // Handle text truncation
  const displayContent = useMemo(() => {
    if (!maxLength || content.length <= maxLength) {
      return processedContent;
    }

    if (isTruncated) {
      const truncatedText = content.slice(0, maxLength) + '...';
      return enableLinkDetection ? truncatedText : truncatedText;
    }

    return processedContent;
  }, [processedContent, maxLength, content.length, isTruncated, enableLinkDetection]);

  // Message bubble classes based on NewChatUI reference design
  const bubbleClasses = [
    'relative px-4 py-2 rounded-2xl text-sm',
    'transition-all duration-200',
    isOwn
      ? `bg-teal-500 text-white ${isDark ? 'rounded-br-none' : 'rounded-br-none'}`
      : isDark
        ? 'bg-gray-700 text-white rounded-bl-none'
        : 'bg-gray-200 text-gray-900 rounded-bl-none',
    onClick ? 'cursor-pointer hover:scale-[1.02]' : '',
    isEditing ? 'ring-2 ring-primary/50' : '',
    className,
  ].filter(Boolean).join(' ');

  return (
    <div className="flex flex-col space-y-1">
      <div 
        className={bubbleClasses}
        onClick={onClick}
      >
        {/* Message content */}
        <div className="whitespace-pre-wrap break-words">
          {Array.isArray(displayContent) 
            ? displayContent.map((element, index) => (
                <React.Fragment key={index}>{element}</React.Fragment>
              ))
            : displayContent
          }
        </div>

        {/* Expand button for truncated messages */}
        {maxLength && content.length > maxLength && isTruncated && onExpand && (
          <Button
            variant="ghost"
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              onExpand();
            }}
            className="mt-1 text-xs"
          >
            Show more
          </Button>
        )}
      </div>

      {/* Message actions */}
        {!isEditing && (
          <div className="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <Button
              variant="ghost"
              size="small"
              icon="copy"
              iconPosition="icon-only"
              onClick={(e) => {
                e.stopPropagation();
                navigator.clipboard.writeText(content);
              }}
              title="Copy message"
              className="p-1"
            />
          </div>
        )}
    </div>
  );
};

export default TextMessage;
