// frontend/src/components/ui/Input.tsx
import React, { forwardRef, useState, useCallback } from 'react';
import { useField, type FieldHookConfig } from 'formik';
import { Icon } from './Icon';
import { Icons } from './Icon';
import { COMPONENT_SIZES } from '../../constants/designSystem';

/**
 * Input component sizes
 */
export type InputSize = 'small' | 'medium' | 'large';

/**
 * Input component types
 */
export type InputType = 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search';

/**
 * Base props for the Input component
 */
export interface BaseInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size' | 'type'> {
  /** Label text for the input */
  label?: string;
  /** Error message to display */
  error?: string | boolean;
  /** Helper text to display below the input */
  helperText?: string;
  /** Icon to display in the input */
  icon?: keyof typeof Icons;
  /** Position of the icon */
  iconPosition?: 'left' | 'right';
  /** Size of the input */
  size?: InputSize;
  /** Type of the input */
  type?: InputType;
  /** Whether the input should take full width */
  fullWidth?: boolean;
  /** Whether to show character count */
  showCharacterCount?: boolean;
  /** Maximum character count */
  maxLength?: number;
}

/**
 * Props for Input component when used with Formik
 */
export interface FormikInputProps extends BaseInputProps {
  /** Formik field name */
  name: string;
}

/**
 * Props for Input component when used standalone
 */
export interface StandaloneInputProps extends BaseInputProps {
  name?: string;
}

export type InputProps = FormikInputProps | StandaloneInputProps;

/**
 * Enhanced Input component with Formik integration and comprehensive styling
 *
 * Features:
 * - Seamless Formik integration with automatic error handling
 * - Multiple input types with validation
 * - Icon support with positioning
 * - Character count display
 * - Glassmorphism design system integration
 * - Comprehensive accessibility support
 * - Loading and disabled states
 *
 * @example
 * ```tsx
 * // With Formik
 * <Input name="email" type="email" label="Email Address" />
 *
 * // Standalone
 * <Input
 *   label="Search"
 *   type="search"
 *   icon="search"
 *   placeholder="Search messages..."
 * />
 * ```
 */
export const Input = forwardRef<HTMLInputElement, InputProps>(({
  label,
  error: errorProp,
  helperText,
  icon,
  iconPosition = 'left',
  size = 'medium',
  type = 'text',
  fullWidth = true,
  showCharacterCount = false,
  maxLength,
  className = '',
  disabled,
  ...props
}, ref) => {
  const [showPassword, setShowPassword] = useState(false);

  // Check if this is a Formik field
  const isFormikField = 'name' in props && props.name;

  // Use Formik field if name is provided
  const [field, meta] = isFormikField
    ? useField(props as FieldHookConfig<string>)
    : [null, null];

  // Determine error state
  const hasError = errorProp || (meta?.touched && meta?.error);
  const errorMessage = typeof errorProp === 'string' ? errorProp : meta?.error;

  // Handle password visibility toggle
  const togglePasswordVisibility = useCallback(() => {
    setShowPassword(prev => !prev);
  }, []);

  // Determine actual input type
  const actualType = type === 'password' && showPassword ? 'text' : type;

  // Size-specific classes
  const sizeClasses = {
    small: 'px-3 py-1.5 text-sm rounded-md min-h-[2rem]',
    medium: 'px-3 py-2 text-sm rounded-lg min-h-[2.5rem]',
    large: 'px-4 py-3 text-base rounded-xl min-h-[3rem]',
  };

  // Icon size based on input size
  const iconSize = {
    small: 14,
    medium: 16,
    large: 18,
  };

  // Base input classes with glassmorphism
  const baseClasses = [
    'block w-full transition-all duration-250',
    'glass-light border-border-glass',
    'text-text-primary placeholder-text-tertiary',
    'focus:border-border-focus focus:ring-2 focus:ring-primary/20',
    'focus:outline-none focus:bg-bg-primary/50',
    'disabled:opacity-50 disabled:cursor-not-allowed',
    sizeClasses[size],
  ].join(' ');

  // Error state classes
  const errorClasses = hasError
    ? 'border-danger focus:border-danger focus:ring-danger/20'
    : '';

  // Icon padding classes
  const iconPaddingClasses = icon || type === 'password'
    ? iconPosition === 'left'
      ? 'pl-10'
      : 'pr-10'
    : '';

  // Handle password field right padding (for both custom icon and password toggle)
  const passwordPaddingClasses = type === 'password'
    ? icon && iconPosition === 'left'
      ? 'pl-10 pr-10'
      : 'pr-10'
    : iconPaddingClasses;

  const finalInputClasses = `${baseClasses} ${errorClasses} ${passwordPaddingClasses} ${className}`;

  // Character count
  const currentLength = field?.value?.length || props.value?.toString()?.length || 0;

  return (
    <div className={`space-y-1 ${fullWidth ? 'w-full' : ''}`}>
      {/* Label */}
      {label && (
        <label
          htmlFor={props.id || props.name}
          className="block text-sm font-medium text-text-primary"
        >
          {label}
          {props.required && <span className="text-danger ml-1">*</span>}
        </label>
      )}

      {/* Input container */}
      <div className="relative">
        {/* Left icon */}
        {icon && iconPosition === 'left' && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Icon
              name={icon}
              className="text-text-tertiary"
              size={iconSize[size]}
            />
          </div>
        )}

        {/* Input field */}
        <input
          ref={ref}
          type={actualType}
          className={finalInputClasses}
          disabled={disabled}
          maxLength={maxLength}
          {...(field || {})}
          {...props}
        />

        {/* Right icon or password toggle */}
        {(icon && iconPosition === 'right') || type === 'password' ? (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
            {type === 'password' ? (
              <button
                type="button"
                className="text-text-tertiary hover:text-text-secondary transition-colors"
                onClick={togglePasswordVisibility}
                disabled={disabled}
                tabIndex={-1}
              >
                <Icon
                  name={showPassword ? 'eyeOff' : 'eye'}
                  size={iconSize[size]}
                />
              </button>
            ) : icon && iconPosition === 'right' ? (
              <Icon
                name={icon}
                className="text-text-tertiary pointer-events-none"
                size={iconSize[size]}
              />
            ) : null}
          </div>
        ) : null}
      </div>

      {/* Helper text, error message, and character count */}
      <div className="flex justify-between items-start">
        <div className="flex-1">
          {/* Error message */}
          {hasError && errorMessage && (
            <p className="text-sm text-danger flex items-center">
              <Icon name="alert" className="mr-1 flex-shrink-0" size={14} />
              {errorMessage}
            </p>
          )}

          {/* Helper text */}
          {!hasError && helperText && (
            <p className="text-sm text-text-secondary">
              {helperText}
            </p>
          )}
        </div>

        {/* Character count */}
        {showCharacterCount && maxLength && (
          <p className={`text-xs ml-2 flex-shrink-0 ${
            currentLength > maxLength ? 'text-danger' : 'text-text-tertiary'
          }`}>
            {currentLength}/{maxLength}
          </p>
        )}
      </div>
    </div>
  );
});

Input.displayName = 'Input';
