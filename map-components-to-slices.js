import fs from "fs";
import path from "path";

const INPUT_FILE = "deps.json";
const OUTPUT_FILE = "component-to-slices.json";

// Load Madge deps
const deps = JSON.parse(fs.readFileSync(INPUT_FILE, "utf8"));

// Filter only components that use store or slices
const result = {};

for (const [file, imports] of Object.entries(deps)) {
  const sliceDeps = imports.filter(
    (imp) => imp.startsWith("store/index.ts") || imp.startsWith("store/slices/")
  );

  if (sliceDeps.length > 0) {
    result[file] = sliceDeps;
  }
}

fs.writeFileSync(OUTPUT_FILE, JSON.stringify(result, null, 2));
console.log(`✅ Component-to-slices mapping written to ${OUTPUT_FILE}`);
